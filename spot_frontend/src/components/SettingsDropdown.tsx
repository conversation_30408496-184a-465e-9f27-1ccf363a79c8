import { Settings } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown } from 'react-icons/fa';

interface SettingsDropdownProps {
  onRecoveryKeyClick: () => void;
  onImportWalletClick: () => void;
  onManageWalletsClick: () => void;
}

const SettingsDropdown: React.FC<SettingsDropdownProps> = ({
  onRecoveryKeyClick,
  onImportWalletClick,
  onManageWalletsClick,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleMenuItemClick = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Settings Icon Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-1 rounded-lg hover:bg-[#1D2226] transition-colors"
        aria-label="Settings"
      >
        {/* <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-6 h-6"
        >
          <path
            d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg> */}
        <svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="40" height="40" rx="10" fill="#181C20"/>
<path d="M28.3462 17.3503L27.9802 17.1463L27.8672 17.0823C27.5946 16.9187 27.3649 16.6924 27.1972 16.4223C27.1792 16.3953 27.1632 16.3663 27.1312 16.3103C26.9156 15.9642 26.8109 15.5605 26.8312 15.1533L26.8372 14.7283C26.8492 14.0483 26.8552 13.7063 26.7592 13.4003C26.6742 13.128 26.5321 12.8771 26.3422 12.6643C26.1282 12.4243 25.8312 12.2523 25.2362 11.9103L24.7422 11.6253C24.1502 11.2843 23.8532 11.1133 23.5382 11.0483C23.2599 10.9907 22.9725 10.9931 22.6952 11.0553C22.3822 11.1253 22.0892 11.3013 21.5042 11.6513L21.5012 11.6533L21.1472 11.8643C21.0912 11.8983 21.0622 11.9143 21.0342 11.9303C20.7562 12.0853 20.4462 12.1703 20.1272 12.1803C20.0952 12.1823 20.0622 12.1823 19.9972 12.1823L19.8672 12.1813C19.5481 12.1711 19.236 12.0847 18.9572 11.9293C18.9292 11.9143 18.9022 11.8973 18.8462 11.8633L18.4892 11.6493C17.9002 11.2953 17.6052 11.1193 17.2902 11.0483C17.0118 10.986 16.7234 10.9839 16.4442 11.0423C16.1282 11.1083 15.8322 11.2803 15.2392 11.6243L15.2362 11.6253L14.7482 11.9083L14.7432 11.9123C14.1552 12.2523 13.8602 12.4243 13.6482 12.6633C13.4593 12.8757 13.3178 13.1259 13.2332 13.3973C13.1382 13.7043 13.1432 14.0463 13.1552 14.7303L13.1622 15.1543C13.1622 15.2193 13.1652 15.2513 13.1642 15.2823C13.159 15.6456 13.0549 16.0007 12.8632 16.3093C12.8302 16.3653 12.8152 16.3933 12.7982 16.4193C12.6296 16.6915 12.3981 16.9192 12.1232 17.0833L12.0112 17.1463L11.6502 17.3463C11.0482 17.6793 10.7472 17.8463 10.5292 18.0843C10.3354 18.2941 10.1887 18.543 10.0992 18.8143C9.99917 19.1213 9.99917 19.4643 10.0002 20.1523L10.0022 20.7153C10.0032 21.3983 10.0052 21.7393 10.1062 22.0443C10.1954 22.3139 10.3409 22.5613 10.5332 22.7703C10.7512 23.0063 11.0492 23.1723 11.6462 23.5043L12.0042 23.7033C12.0652 23.7373 12.0962 23.7533 12.1252 23.7713C12.4385 23.9591 12.6945 24.2293 12.8652 24.5523L12.9322 24.6723C13.1011 24.9912 13.1806 25.3499 13.1622 25.7103L13.1552 26.1173C13.1432 26.8033 13.1382 27.1473 13.2342 27.4543C13.3192 27.7263 13.4612 27.9773 13.6512 28.1903C13.8652 28.4303 14.1632 28.6013 14.7572 28.9443L15.2512 29.2293C15.8442 29.5703 16.1402 29.7413 16.4552 29.8063C16.7335 29.8639 17.0209 29.8615 17.2982 29.7993C17.6122 29.7293 17.9052 29.5533 18.4922 29.2013L18.8462 28.9893L18.9592 28.9233C19.2372 28.7693 19.5472 28.6833 19.8662 28.6733L19.9962 28.6723H20.1262C20.4442 28.6823 20.7562 28.7693 21.0362 28.9243L21.1282 28.9793L21.5042 29.2053C22.0942 29.5593 22.3882 29.7353 22.7032 29.8053C22.9814 29.8683 23.2698 29.871 23.5492 29.8133C23.8642 29.7473 24.1622 29.5743 24.7552 29.2303L25.2502 28.9433C25.8382 28.6013 26.1332 28.4303 26.3452 28.1913C26.5352 27.9783 26.6752 27.7283 26.7602 27.4573C26.8552 27.1523 26.8502 26.8133 26.8382 26.1393L26.8302 25.6993V25.5723C26.8349 25.2087 26.9386 24.8533 27.1302 24.5443L27.1952 24.4343C27.3638 24.1621 27.5953 23.9344 27.8702 23.7703L27.9802 23.7093L27.9822 23.7083L28.3432 23.5083C28.9452 23.1743 29.2462 23.0083 29.4652 22.7703C29.6592 22.5603 29.8052 22.3103 29.8942 22.0403C29.9942 21.7353 29.9942 21.3933 29.9922 20.7133L29.9902 20.1393C29.9892 19.4563 29.9882 19.1143 29.8872 18.8093C29.7976 18.54 29.6517 18.2929 29.4592 18.0843C29.2422 17.8483 28.9442 17.6823 28.3482 17.3513L28.3462 17.3503Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M16 20.4272C16 21.4881 16.4214 22.5055 17.1716 23.2557C17.9217 24.0058 18.9391 24.4272 20 24.4272C21.0609 24.4272 22.0783 24.0058 22.8284 23.2557C23.5786 22.5055 24 21.4881 24 20.4272C24 19.3664 23.5786 18.349 22.8284 17.5988C22.0783 16.8487 21.0609 16.4272 20 16.4272C18.9391 16.4272 17.9217 16.8487 17.1716 17.5988C16.4214 18.349 16 19.3664 16 20.4272Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-[#181C20] border border-gray-600 rounded-xl shadow-2xl z-50">
          {/* Account and Security Section */}
          <div className="p-4">
            <h3 className="text-gray-400 text-sm font-medium mb-3 uppercase tracking-wide">
              Account and Security
            </h3>
            
            {/* Recovery Key Option */}
            <button
              onClick={() => handleMenuItemClick(onRecoveryKeyClick)}
              className="w-full text-left p-3 rounded-lg hover:bg-[#1D2226] transition-colors group"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-[#14FFA2]/20 rounded-lg flex items-center justify-center">
                  <svg 
                    width="16" 
                    height="16" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path 
                      d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" 
                      stroke="#14FFA2" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                    <path 
                      d="M9 12L11 14L15 10" 
                      stroke="#14FFA2" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div>
                  <div className="text-white font-medium">Recovery Key</div>
                  <div className="text-gray-400 text-sm">Export your wallet's private key</div>
                </div>
              </div>
            </button>

            {/* Import Wallet Option */}
            <button
              onClick={() => handleMenuItemClick(onImportWalletClick)}
              className="w-full text-left p-3 rounded-lg hover:bg-[#1D2226] transition-colors group mt-2"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                      stroke="#3B82F6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M14 2V8H20"
                      stroke="#3B82F6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M12 18V12"
                      stroke="#3B82F6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M9 15L12 12L15 15"
                      stroke="#3B82F6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div>
                  <div className="text-white font-medium">Import a Wallet</div>
                  <div className="text-gray-400 text-sm">Import an existing wallet by private key</div>
                </div>
              </div>
            </button>

            {/* Manage Wallets Option */}
            <button
              onClick={() => handleMenuItemClick(onManageWalletsClick)}
              className="w-full text-left p-3 rounded-lg hover:bg-[#1D2226] transition-colors group mt-2"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M21 16V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V16C3 17.1046 3.89543 18 5 18H19C20.1046 18 21 17.1046 21 16Z"
                      stroke="#8B5CF6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M7 10H17"
                      stroke="#8B5CF6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M7 14H11"
                      stroke="#8B5CF6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M15 14H17"
                      stroke="#8B5CF6"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <div>
                  <div className="text-white font-medium">Manage Wallets</div>
                  <div className="text-gray-400 text-sm">Set defaults and manage connected wallets</div>
                </div>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsDropdown;
