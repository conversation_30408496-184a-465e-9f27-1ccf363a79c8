import React, { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "./navbar-shine.css"; // Import CSS for shine animation
import { FaSearch, FaCopy, FaCheck, FaBars, FaTimes } from "react-icons/fa";
import { BiWallet } from "react-icons/bi";
import { Settings, Settings2 } from "lucide-react";
import { usePrivy, useWallets } from '@privy-io/react-auth';
import SearchModal from "./SearchModal";
import SettingsDropdown from "../../components/SettingsDropdown";
import RecoveryKeyModal from "../../components/RecoveryKeyModal";
import ImportWalletModal from "../../components/ImportWalletModal";
import ManageWalletsModal from "../../components/ManageWalletsModal";
import { formatTokenBalance, TokenBalance } from "../../utils/tokenBalances";
import { getSolanaTokenBalances } from "../../api/getSolToken";
import { homeAPI } from "../../utils/api";
import { tokenBalanceCache } from "../../utils/tokenBalanceCache";
import axios from "axios";
// SVG imports
import NotificationIcon from "../../assets/notification.svg";
import HelpIcon from "../../assets/help.svg";
import DocumentsIcon from "../../assets/documents.svg";
import SettingsIcon from "../../assets/settings.svg";
import ProfileIcon from "../../assets/profile.svg";
import Languages from "../../assets/language.svg";
import { usePulseData } from "../../hooks/usePulseData";
import { getSwapForExchange, SwapRequest } from "../../api/solana_api";
import { getDefaultSolanaWalletInfo } from "../../utils/walletUtils";
import SimpleNavbarMonitor from "@/components/PerformanceMonitor/SimpleNavbarMonitor";
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from "../../utils/swapToast";
import { notificationAPI } from "../../api/notificationApi";
import { formatDistanceToNow } from 'date-fns';

// Define types for wallet addresses
interface TokenAddress {
  address: string;
  type: string;
  chainName: string;
  walletType?: string;
}


const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>("");
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  const [isSearchOpen, setIsSearchOpen] = useState<boolean>(false);
  const [isProfileOpen, setIsProfileOpen] = useState<boolean>(false);
  const [isWalletDropdownOpen, setIsWalletDropdownOpen] = useState<boolean>(false);
  const { user, logout, authenticated, connectWallet } = usePrivy();
  const { wallets } = useWallets();
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const [userAddress, setUserAddress] = useState<string | null>(null);
  const [isRecoveryKeyModalOpen, setIsRecoveryKeyModalOpen] = useState<boolean>(false);
  const [isImportWalletModalOpen, setIsImportWalletModalOpen] = useState<boolean>(false);
  const [isManageWalletsModalOpen, setIsManageWalletsModalOpen] = useState<boolean>(false);
  const [tokenBalances, setTokenBalances] = useState<TokenBalance[]>([]);
  const [loadingBalances, setLoadingBalances] = useState<boolean>(false);
  const [tokenSearch, setTokenSearch] = useState('');
  const [solBalance, setSolBalance] = useState<number | null>(null);
  const walletDropdownRef = useRef<HTMLDivElement | null>(null);
  const { setActiveToken } = usePulseData();
  
  // Notification states
  const [isNotificationOpen, setIsNotificationOpen] = useState<boolean>(false);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loadingNotifications, setLoadingNotifications] = useState<boolean>(false);
  const notificationDropdownRef = useRef<HTMLDivElement | null>(null);
  
  // Handle token click to navigate to Pulse Trade page
  const handleTokenClick = async (token: TokenBalance) => {
    console.log('Token clicked:', token);

    // Close the dropdown
    setIsWalletDropdownOpen(false);

    if (!token || !token.mintAddress) {
      console.error('Navigation failed: mintAddress is missing from token.', token);
      return;
    }

    const mintAddress = token.mintAddress; // TypeScript now knows this is a string

    try {
      console.log(`Searching for token data with mintAddress: ${mintAddress}`);
      const searchResponse = await homeAPI.fetchSearchResults(mintAddress);
      
      let foundTokenData = null;

      if (searchResponse && searchResponse.data && searchResponse.data.data) {
        const searchResults = searchResponse.data.data;
        console.log('Search API response:', searchResults);
        
        if (Array.isArray(searchResults)) {
          for (const result of searchResults) {
            if (result.pairs && Array.isArray(result.pairs)) {
              for (const pair of result.pairs) {
                const token0 = pair.token0;
                const token1 = pair.token1;

                if ((token1 && token1.address === mintAddress) || (token0 && token0.address === mintAddress)) {
                  const matchedToken = token1.address === mintAddress ? token1 : token0;
                  
                  foundTokenData = {
                    id: matchedToken.address,
                    address: matchedToken.address,
                    name: matchedToken.name,
                    symbol: matchedToken.symbol,
                    price: parseFloat(pair.price) || 0,
                    market_cap: parseFloat(pair.price) * parseFloat(matchedToken.circulatingSupply) || 0, // This can be enriched later
                    volume: parseFloat(pair.volume?.h24 || '0'),
                    liquidity: parseFloat(pair.liquidity?.usd || '0'),
                    supply: parseFloat(matchedToken.circulatingSupply || '0'),
                    exchange_name: pair.exchange.name || '',
                    pool_address: pair.address || '',
                    price_change_24h: parseFloat(pair.priceChange?.h24 || '0'),
                    bonding: 0,
                    imageUrl: matchedToken.logo || '',
                    network: 'solana',
                    exchange_logo: pair.exchange?.logo || ''
                  };
                  break;
                }
              }
            }
            if (foundTokenData) break;
          }
        }
      }
      
      if (foundTokenData) {
        console.log('Found full token data from search:', foundTokenData);
        setActiveToken(null, foundTokenData);
      } else {
        console.log('Could not find full token data. Using basic info.');
        const pulseToken = {
          id: mintAddress,
          address: mintAddress,
          name: token.name,
          symbol: token.symbol,
          price: 0,
          market_cap: 0,
          volume: 0,
          liquidity: 0,
          supply: 0,
          exchange_name: '',
          pool_address: '',
          price_change_24h: 0,
          bonding: 0,
          imageUrl: token.logo || '',
          network: 'solana',
          exchange_logo: ''
        };
        setActiveToken(null, pulseToken);
      }

    } catch (error) {
      console.error('Error fetching token data, using basic info as fallback:', error);
      const pulseToken = {
        id: mintAddress,
        address: mintAddress,
        name: token.name,
        symbol: token.symbol,
        price: 0,
        market_cap: 0,
        volume: 0,
        liquidity: 0,
        supply: 0,
        exchange_name: '',
        pool_address: '',
        price_change_24h: 0,
        bonding: 0,
        imageUrl: token.logo || '',
        network: 'solana',
        exchange_logo: ''
      };
      setActiveToken(null, pulseToken);
    }

    // Navigate to the trade page
    console.log(`Navigating to /trade/${mintAddress}`);
    navigate(`/trade/${mintAddress}`);
  };

  const handleSellAll = async (token: TokenBalance) => {
    if (!token.mintAddress) {
      showSwapErrorToast("Token has no mint address, cannot proceed with the sale.");
      return;
    }

    const mintAddress = token.mintAddress; // TypeScript now knows this is a string

    // 1. Get user's wallet.
    const solanaWallets = wallets.filter(w => w.walletClientType === 'solana' || !w.address.startsWith('0x'));
    const walletInfo = await getDefaultSolanaWalletInfo(authenticated, user?.id, solanaWallets, user);

    if (!walletInfo) {
      showSwapErrorToast("No connected Solana wallet found to perform the swap.");
      return;
    }

    // 2. We need the dexType. We can get it by fetching the token's data.
    const searchResponse = await homeAPI.fetchSearchResults(mintAddress);
    let dexType: string | null = null;
    let poolAddress: string | null = null;

    if (searchResponse && searchResponse.data && searchResponse.data.data) {
      const searchResults = searchResponse.data.data;
      if (Array.isArray(searchResults)) {
        for (const result of searchResults) {
          if (result.pairs && Array.isArray(result.pairs)) {
            for (const pair of result.pairs) {
              const token0 = pair.token0;
              const token1 = pair.token1;

              if ((token1 && token1.address === mintAddress) || (token0 && token0.address === mintAddress)) {
                if (pair.exchange?.name) {
                  dexType = pair.exchange.name.toLowerCase().replace(/[^a-z]/g, '');
                } else {
                  dexType = null;
                }
                poolAddress = pair.address || null;
                break;
              }
            }
          }
          if (dexType && poolAddress) {
            break;
          }
        }
      }
    }

    if (!dexType || !poolAddress) {
      showSwapErrorToast("Could not determine the exchange for this token. Unable to sell.");
      return;
    }

    // 3. Construct SwapRequest.
    const swapRequest: SwapRequest = {
      walletAddress: walletInfo.address,
      walletId: walletInfo.id,
      tokenAddress: mintAddress,
      poolAddress: poolAddress,
      dexType: dexType,
      amount: parseFloat(token.balance),
      direction: 'sell',
      slippage: 0.2, // Using a default slippage of 10% for now

      // Token metadata from wallet balance (efficient - no backend API calls needed)
      tokenName: token.name,
      tokenSymbol: token.symbol,
      tokenImage: token.logo,

      // Default MEV/Priority settings from PulseTrade page
      mevProtection: false,
      bribeAmount: 0.00005,
      priorityLevel: 'low',
      priorityFee: 0.0001,
    };

    // 4. Call getSwapForExchange.
    try {
      showSwapInfoToast("Selling all your tokens, please wait...");
      const swapResponse = await getSwapForExchange(dexType, swapRequest);

      if (swapResponse.success && swapResponse.data) {
        showSwapSuccessToast('sell', token.symbol, swapResponse.data.signature || swapResponse.data.transactionHash || '');
        // 5. Handle response and update UI.
        fetchTokenBalances(); // Refresh balances
      } else {
        showSwapErrorToast(swapResponse.error || "Failed to sell tokens.");
      }
    } catch (error: any) {
      showSwapErrorToast(error.message || "An unexpected error occurred during the swap.");
    }
  };
  
  // Debug wallet state and fetch token balances
  useEffect(() => {
    if (authenticated) {
      console.log("Navbar - User authenticated:", authenticated);
      console.log("Navbar - User object:", user);
      if (user?.email) {
        console.log("Navbar - User email:", user.email);
      }
      console.log("Navbar - Wallets:", wallets);
      
      // Check if wallets array exists and has entries
      if (wallets && wallets.length > 0) {
        console.log("Navbar - Wallet details:", wallets.map(w => ({
          address: w.address,
          type: w.walletClientType,
          connected: w.connectedAt, // Fixed: changed connected to connectedAt
          chainId: w.chainId
          // Removed chains property
        })));
        
        // Set the first wallet address as the user address for display
        if (wallets[0]?.address) {
          setUserAddress(wallets[0].address);
          console.log("User address set from wallet:", wallets[0].address);
          
          // Store Solana wallet address in localStorage for API access
          if (!wallets[0].address.startsWith('0x')) {
            // This is a Solana address
            const defaultWallets = {
              solana: wallets[0].address
            };
            console.log('Storing Solana address in localStorage:', defaultWallets);
            localStorage.setItem('defaultWallets', JSON.stringify(defaultWallets));
          }
          
          // Fetch token balances for the wallet
          fetchTokenBalances();
        }
      } else {
        console.log("Navbar - No wallets available");
      }
    }
  }, [authenticated, user, wallets]);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (walletDropdownRef.current && !walletDropdownRef.current.contains(event.target as Node)) {
        setIsWalletDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Helper function to sort tokens by balance (descending)
  const sortTokensByBalance = (tokens: TokenBalance[]): TokenBalance[] => {
    // Create a copy of the array to avoid mutating the original
    return [...tokens].sort((a, b) => {
      // Convert string balances to numbers for comparison
      const balanceA = parseFloat(a.balance) / Math.pow(10, a.decimals);
      const balanceB = parseFloat(b.balance) / Math.pow(10, b.decimals);
      return balanceB - balanceA; // Sort in descending order
    });
  };
  
  // Filtered token balances based on search
  const filteredTokenBalances = useMemo(() => {
    if (!tokenSearch.trim()) return tokenBalances;
    return tokenBalances.filter(token => {
      return (
        token.symbol.toLowerCase().includes(tokenSearch.toLowerCase()) ||
        token.name.toLowerCase().includes(tokenSearch.toLowerCase())
      );
    });
  }, [tokenBalances, tokenSearch]);
  
  // Fetch token balances from the backend API
  const fetchTokenBalances = async () => {
    if (!authenticated) {
      console.log('Cannot fetch token balances: not authenticated');
      return;
    }
    
    // Get wallet address using the same logic as Portfolio
    let walletAddress: string | null = null;
    
    // First try to get from useWallets hook
    const solanaWallet = wallets.find(wallet => wallet.walletClientType === 'solana');
    if (solanaWallet?.address) {
      walletAddress = solanaWallet.address;
      console.log('[Navbar] Got Solana wallet from useWallets:', walletAddress);
    } else if (user?.linkedAccounts) {
      // Fallback to linkedAccounts
      const solanaAccount = user.linkedAccounts.find(
        account => account.type === 'wallet' && 
        (account as any).walletClientType === 'solana' &&
        (account as any).address
      );
      if (solanaAccount && (solanaAccount as any).address) {
        walletAddress = (solanaAccount as any).address;
        console.log('[Navbar] Got Solana wallet from linkedAccounts:', walletAddress);
      }
    }
    
    // Final fallback to localStorage
    if (!walletAddress) {
      walletAddress = await getSolanaTokenBalances().then(res => 
        res.data ? (res.data as any).walletAddress : null
      ).catch(() => null);
      
      // If still no address, try getDefaultSolanaWalletInfo
      if (!walletAddress) {
        const walletInfoFallback = await getDefaultSolanaWalletInfo(authenticated, user?.id, wallets.filter(w => w.walletClientType === 'solana' || !w.address.startsWith('0x')), user);
        walletAddress = walletInfoFallback?.address || null;
      }
      
      if (walletAddress) {
        console.log('[Navbar] Got Solana wallet from localStorage:', walletAddress);
      }
    }
    
    if (!walletAddress) {
      console.log('[Navbar] No wallet address found');
      setLoadingBalances(false);
      return;
    }
    
    // Always show loading state when fetching
    setLoadingBalances(true);
    setTokenBalances([]); // Clear existing balances while loading
    setSolBalance(null); // Clear SOL balance while loading
    
    try {
      // Check cache first
      const cachedData = tokenBalanceCache.get(walletAddress);
      if (cachedData) {
        console.log('[Navbar] Using cached token balance data');
        if (cachedData.success && cachedData.data) {
          const responseData = cachedData.data;
          // Process cached data same as API response
          processTokenBalanceData(responseData);
          setLoadingBalances(false);
          return;
        }
      }
      
      // Check if there's already a pending request
      const pendingRequest = tokenBalanceCache.getPendingRequest(walletAddress);
      if (pendingRequest) {
        console.log('[Navbar] Using pending request for wallet:', walletAddress);
        const response = await pendingRequest;
        if (response.data?.success && response.data?.data) {
          processTokenBalanceData(response.data.data);
        }
        setLoadingBalances(false);
        return;
      }
      
      // Make the API request and cache it
      console.log('[Navbar] Fetching real Solana token balances from backend API...');
      const requestPromise = axios.post(
        `/api/wallet/solana-token-balance`,
        { walletAddress },
        {
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );
      
      tokenBalanceCache.setPendingRequest(walletAddress, requestPromise);
      const response = await requestPromise;
      tokenBalanceCache.clearPendingRequest(walletAddress);
      
      console.log('[Navbar] Solana token balance API response:', response.data);
      
      // Cache the successful response
      if (response.data?.success) {
        tokenBalanceCache.set(walletAddress, response.data);
      }
      
      if (response.data?.success && response.data?.data) {
        const responseData = response.data.data;
        processTokenBalanceData(responseData);
      } else {
        console.error('[Navbar] Invalid or unsuccessful API response:', response.data);
        setTokenBalances([]);
      }
    } catch (error) {
      console.error('[Navbar] Exception while fetching token balances:', error);
      setTokenBalances([]);
    } finally {
      setLoadingBalances(false);
    }
  };
  
  // Helper function to process token balance data
  const processTokenBalanceData = (responseData: any) => {
    // Extract the SOL balance directly from the response
    // The backend response structure is:
    // { success: true, message: string, data: { success: true, sol: { lamports: number, sol: number }, ... } }
    if (responseData.sol && typeof responseData.sol.sol === 'number') {
      console.log('Setting SOL balance from API:', responseData.sol.sol);
      setSolBalance(responseData.sol.sol);
    } else if (responseData.sol && responseData.sol.balance) {
      // Alternative structure where balance is a string
      console.log('Setting SOL balance from API (string):', responseData.sol.balance);
      setSolBalance(parseFloat(responseData.sol.balance));
    } else {
      console.warn('SOL balance not found in the expected format:', responseData);
    }

    // Create separate arrays for SOL and other tokens
    let solToken: TokenBalance | null = null;
    const otherTokens: TokenBalance[] = [];
    
    // Verify and extract SOL balance if available
    if (responseData.sol) {
      const solBalance = responseData.sol.sol || parseFloat(responseData.sol.balance) || 0;
      solToken = {
        symbol: 'SOL',
        name: 'Solana',
        balance: solBalance.toString(),
        decimals: 9,
        mintAddress: 'So11111111111111111111111111111111111111112',
        logo: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
      };
    }
    
    // Add all SPL tokens from the API if they exist
    if (responseData.tokens && Array.isArray(responseData.tokens) && responseData.tokens.length > 0) {
      console.log(`Mapping ${responseData.tokens.length} SPL tokens from API response`);
      
      responseData.tokens.forEach((token: any) => {
        if (token.symbol && token.name) {
          otherTokens.push({
            symbol: token.symbol,
            name: token.name,
            balance: token.displayBalance || token.balance || '0',
            decimals: token.decimals || 0,
            mintAddress: token.mintAddress || '', 
            logo: token.logo || ''
          });
        }
      });
    }
    
    // Sort other tokens by balance
    const sortedOtherTokens = sortTokensByBalance(otherTokens);
    
    // Combine the arrays with SOL first, then other tokens sorted by balance
    const finalTokenBalances: TokenBalance[] = [];
    if (solToken) {
      finalTokenBalances.push(solToken);
    }
    finalTokenBalances.push(...sortedOtherTokens);
    
    console.log(`Successfully mapped ${finalTokenBalances.length} token balances from API`);
    // Only update state if we have tokens
    if (finalTokenBalances.length > 0) {
      setTokenBalances(finalTokenBalances);
    } else {
      console.warn('No token balances found in API response');
      setTokenBalances([]);
    }
  };
  

  // Notification functions
  const loadUnreadCount = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      const response = await notificationAPI.getUnreadCount(user.id);
      if (response.success) {
        setUnreadCount(response.data.unread_count);
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  }, [user?.id]);

  const loadNotifications = async () => {
    if (!user?.id) return;
    
    setLoadingNotifications(true);
    try {
      const response = await notificationAPI.getNotifications(user.id, 20, 0, false);
      if (response.success) {
        // Handle the current backend response structure where data is directly the array
        const notificationData = Array.isArray(response.data) ? response.data : response.data.notifications || [];
        // Add read property since backend doesn't have is_read field
        const notificationsWithReadStatus = notificationData.map(notification => ({
          ...notification,
          read: false // Since backend doesn't have is_read, treat all as unread
        }));
        setNotifications(notificationsWithReadStatus);
        
        // Get unread count separately
        const unreadResponse = await notificationAPI.getUnreadCount(user.id);
        if (unreadResponse.success) {
          setUnreadCount(unreadResponse.data.unread_count || 0);
        }
      } else {
        // Ensure notifications is always an array, never undefined
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      // Ensure notifications is always an array on error
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoadingNotifications(false);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    if (!user?.id) return;
    
    try {
      // Since backend doesn't support marking as read yet, just update frontend state
      setNotifications(prev => 
        (prev || []).map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
      
      // Uncomment this when backend supports marking as read
      // const result = await notificationAPI.markAsRead(notificationId, user.id);
      // if (!result.success) {
      //   console.error('Failed to mark notification as read on backend');
      // }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user?.id) return;
    
    try {
      // Since backend doesn't support marking as read yet, just update frontend state
      setNotifications(prev => (prev || []).map(n => ({ ...n, read: true })));
      setUnreadCount(0);
      
      // Uncomment this when backend supports marking all as read
      // const result = await notificationAPI.markAllAsRead(user.id);
      // if (!result.success) {
      //   console.error('Failed to mark all notifications as read on backend');
      // }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const handleNotificationClick = () => {
    setIsNotificationOpen(!isNotificationOpen);
    if (!isNotificationOpen) {
      loadNotifications();
    }
  };

  // ✅ Restore activeTab from localStorage on mount
  useEffect(() => {
    const storedTab = localStorage.getItem("activeTab");
    if (storedTab && (storedTab === "Pulse" || storedTab === "Portfolio")) {
      setActiveTab(storedTab);
    } else {
      // Default to Pulse if no valid stored tab
      setActiveTab("Pulse");
      localStorage.setItem("activeTab", "Pulse");
    }
  }, []);

  // Helper function to validate if a string looks like a valid Solana address
  const isValidSolanaAddress = (address: string): boolean => {
    if (!address || typeof address !== 'string') return false;
    
    // Exclude anything that looks like an email
    if (address.includes('@') || address.includes('.com') || address.includes('.io') || address.includes('.org')) {
      console.log(`Rejecting email-like address: ${address}`);
      return false;
    }
    
    // Basic check for Solana addresses - typically 32-44 chars base58
    return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address) && !address.startsWith('0x');
  };
  

  // Function to get all available Solana wallet addresses
  const getSolanaWalletAddresses = (): TokenAddress[] => {
    const addresses: TokenAddress[] = [];
    const seenAddresses = new Set();
    
    console.log("Getting Solana wallet addresses from all sources...");
    
    // Helper to add an address to our results
    const addUniqueAddress = (address: string): boolean => {
      // First validate this is actually a Solana address
      if (!isValidSolanaAddress(address)) {
        console.log(`Rejecting invalid address format: ${address}`);
        return false;
      }
      
      const normalizedAddress = address.toLowerCase();
      
      // Skip if we've already added this address
      if (seenAddresses.has(normalizedAddress)) {
        console.log(`Skipping duplicate address: ${address}`);
        return false;
      }
      
      console.log(`Adding Solana wallet address: ${address}`);
      seenAddresses.add(normalizedAddress);
      
      addresses.push({
        address: address,
        type: 'solana',
        chainName: 'Solana',
        walletType: 'external'
      });
      
      return true;
    };
    
    // Check linked accounts from user object - look for Solana wallets
    if (user?.linkedAccounts && user.linkedAccounts.length > 0) {
      console.log(`Found ${user.linkedAccounts.length} linked accounts in user object`);
      
      for (const account of user.linkedAccounts) {
        // Check if account has an address property safely
        const accountAddress = (account as any).address;
        
        if (accountAddress && isValidSolanaAddress(accountAddress)) {
          const accountType = (account as any).type;
          
          if (accountType === 'wallet') {
            console.log(`Found Solana wallet: ${accountAddress.slice(0, 6)}...${accountAddress.slice(-4)}`);
            addUniqueAddress(accountAddress);
          }
        }
      }
    }
    
    // Check wallets from useWallets hook - only include Solana wallets
    if (wallets && wallets.length > 0) {
      const solanaWallets = wallets.filter(w => {
        // Check if it's connected
        if (!w.connectedAt) return false;
        
        // Only include Solana wallets
        return w.address && isValidSolanaAddress(w.address);
      });
      
      console.log(`Found ${solanaWallets.length} Solana wallets from useWallets hook`);
      
      solanaWallets.forEach(wallet => {
        if (wallet.address) {
          console.log(`Processing Solana wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)} (${wallet.walletClientType})`);
          addUniqueAddress(wallet.address);
        }
      });
    }
    
    console.log("Final unique addresses:", addresses);
    return addresses;
  };

  // Copy wallet address to clipboard
  const copyToClipboard = (address: string): void => {
    navigator.clipboard.writeText(address).then(() => {
      setCopiedAddress(address);
      // Reset copy icon after 2 seconds
      setTimeout(() => setCopiedAddress(null), 2000);
    });
  };

  // Helper function to get user display info
  const getUserDisplayInfo = () => {
    if (!authenticated || !user) return { displayName: "Guest", address: null, userId: null };
    
    // Debug user object to find email
    // console.log("Getting user display info from:", user);
    
    // Check all possible locations for email
    let email = null;
    
    // Check email property - handle case where it's an object
    if (user.email?.address) {
      email = user.email.address.toString();
      // console.log("Found email:", email);
    } 
    // Check linked accounts for email
    else if (user.linkedAccounts && user.linkedAccounts.length > 0) {
      const emailAccount = user.linkedAccounts.find(account => 
        account.type === 'email' || ((account as any).address && (account as any).address.includes('@'))
      );
      if (emailAccount) {
        email = (emailAccount as any).address || (emailAccount as any).email;
        console.log("Found email in linkedAccounts:", email);
      }
    }
    
    const displayName = "Connected User";
    
    // Get user ID and clean it up - remove did:privy: prefix
    let rawUserId = user.id || (user as any).userId || user.wallet?.address;
    let userId = rawUserId;
    
    // Remove did:privy: prefix if it exists
    if (typeof userId === 'string' && userId.startsWith('did:privy:')) {
      userId = userId.replace('did:privy:', '');
    }
    
    console.log("Raw user ID:", rawUserId);
    console.log("Cleaned user ID:", userId);
    
    return { displayName, userId, email };
  };

  useEffect(() => {
    const currentPath = location.pathname;
  
    const tabMap: Record<string, string> = {
      "/pulse": "Pulse",
      "/portfolio": "Portfolio",
    };
  
    // If the route matches one of the tabs
    if (tabMap[currentPath]) {
      const activeRoute = tabMap[currentPath];
      setActiveTab(activeRoute);
      localStorage.setItem("activeTab", activeRoute);
    }
    // Default to Pulse for unknown routes
    else {
      setActiveTab("Pulse");
      localStorage.setItem("activeTab", "Pulse");
    }
  }, [location.pathname]);
  
  // Load unread notification count when user is authenticated
  useEffect(() => {
    if (authenticated && user?.id) {
      loadUnreadCount();
      // Reload unread count every 30 seconds
      const interval = setInterval(loadUnreadCount, 30000);
      return () => clearInterval(interval);
    }
  }, [authenticated, user?.id, loadUnreadCount]);

  // Handle click outside notification dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setIsNotificationOpen(false);
      }
    };

    if (isNotificationOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isNotificationOpen]);

  const handleTabClick = (tab: string): void => {
    setActiveTab(tab);
    localStorage.setItem("activeTab", tab);

    if(tab=== "Pulse"){
      navigate("/pulse")
    }
    else if(tab==='Portfolio'){
      navigate("/portfolio")
    }
  };

  const userInfo = getUserDisplayInfo();
  // Get all available Solana wallet addresses
  const solanaAddresses = getSolanaWalletAddresses();
 
  // Helper function to format addresses
  const formatAddress = (address: string): string => {
    if (address.length <= 12) {
      return address;
    }
    const start = address.slice(0, 6);
    const end = address.slice(-4);
    return `${start}...${end}`;
  };
 
  return (
    <nav className="bg-[#141416] text-white flex flex-wrap items-center justify-between py-3 px-[20px]">
    {/* Mobile Menu Button (only visible on small screens) */}
    <div className="lg:hidden flex items-center">
      <button 
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        className="text-white p-2"
      >
        {mobileMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
      </button>
    </div>

    {/* Left: Logo and Navigation */}
    <div className="flex items-center">
      <div className="text-2xl font-bold flex items-center justify-center">
        <svg width="23" height="30" viewBox="0 0 23 30" fill="none" xmlns="http://www.w3.org/2000/svg" className="my-auto">
          <path d="M16.0018 0H0V12.3855L4.25571 8.39106V4.3054H14.6038C16.844 4.3054 18.6595 6.13966 18.6595 8.40317V12.9693C18.6595 15.2328 16.844 17.067 14.6038 17.067H7.06274L15.8442 8.4432H8.79709L0 17.013V21.4739L8.74088 30H15.811L7.01943 21.3696H16.0801C19.9027 21.3184 22.9769 18.0559 22.9852 14.1927L23 7.08566C23.0083 3.17505 19.8722 0 16.0018 0Z" fill="white"/>
        </svg>
      </div>
      
      {/* Divider - Only visible on desktop */}
      <div className="hidden lg:block border-r border-white h-6 mx-[25px]"></div>

      {/* Navigation Tabs - Desktop */}
      <ul className="hidden lg:flex items-center gap-[30px]">
  {["Pulse", "Portfolio"].map((tab) => (
    <li
      key={tab}
      onClick={() => handleTabClick(tab)}
      className={`px-[15px] py-2 rounded-3xl font-medium cursor-pointer 
        transition-all duration-300 ease-in-out transform
        ${
          activeTab === tab
            ? "bg-white text-gray-600 scale-105 shadow-md"
            : "text-white hover:scale-105 hover:text-gray-300"
        }`}
    >
      {tab}
    </li>
  ))}
</ul>

    </div>

    {/* Center: Search Bar - only on medium screens and up */}
    <div className="hidden md:block relative w-1/3 mx-4">
      <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500" />
      <input
        type="text"
        placeholder="Search..."
        className="bg-[#181C20] text-white pl-12 pr-4 py-3 rounded-3xl w-full outline-none"
        onClick={() => setIsSearchOpen(true)}
        readOnly
      />
    </div>

    {/* Mobile Search Icon - visible only on small screens */}
    <div className="md:hidden">
      <button 
        onClick={() => setIsSearchOpen(true)}
        className="p-2 rounded-full bg-[#181C20]"
      >
        <FaSearch className="text-gray-500" />
      </button>
    </div>

    {/* Right: Icons - Reduced set for mobile */}
    <div className="flex items-center gap-2 md:gap-4">
      {/* Only show profile and notification icons on small screens */}
      <div className="lg:hidden flex items-center gap-2">
        <SimpleNavbarMonitor />
        <div className="relative" ref={notificationDropdownRef}>
          <button
            onClick={handleNotificationClick}
            className="relative flex items-center justify-center w-8 h-8 rounded-lg hover:bg-[#1D2226] transition-colors"
          >
            <img src={NotificationIcon} alt="Notification" className="w-8 h-8" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] text-center leading-none">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>
          
          {/* Notification Dropdown */}
          {isNotificationOpen && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-[998]" 
                onClick={() => setIsNotificationOpen(false)}
              />
              
              {/* Dropdown */}
              <div className="absolute right-0 mt-2 w-96 bg-[#101114] rounded-xl shadow-lg border border-gray-700 z-[999] max-h-[500px] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700">
                  <div className="flex items-center gap-2">
                    <img src={NotificationIcon} alt="Notifications" className="w-5 h-5" />
                    <h3 className="text-white font-semibold">Notifications</h3>
                    {unreadCount > 0 && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                        {unreadCount}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {unreadCount > 0 && (
                      <button
                        onClick={handleMarkAllAsRead}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                        title="Mark all as read"
                      >
                        <FaCheck className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => setIsNotificationOpen(false)}
                      className="text-gray-400 hover:text-white transition-colors p-1"
                    >
                      <FaTimes className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto">
                  {loadingNotifications ? (
                    <div className="p-4 text-center text-gray-400">
                      <div className="animate-spin w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full mx-auto mb-2"></div>
                      Loading notifications...
                    </div>
                  ) : !notifications || notifications.length === 0 ? (
                    <div className="p-8 text-center text-gray-400">
                      <img src={NotificationIcon} alt="No notifications" className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p className="text-lg font-medium mb-1">No notifications yet</p>
                      <p className="text-sm">You'll see your trading activity and updates here</p>
                    </div>
                  ) : (
                    <div className="py-2">
                      {(notifications || []).map((notification) => (
                        <div
                          key={notification.id}
                          className={`relative px-4 py-3 hover:bg-[#1A1D21] border-b border-gray-800 last:border-b-0 cursor-pointer ${
                            !notification.read ? 'bg-[#1A1D21]/50' : ''
                          }`}
                          onClick={() => {
                            if (!notification.read) {
                              handleMarkAsRead(notification.id);
                            }
                          }}
                        >
                          {/* Unread indicator */}
                          {!notification.read && (
                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                          
                          <div className="flex items-start gap-3 ml-2">
                            {/* Icon */}
                            <div className="text-2xl mt-1">
                              {notification.activity_type === 'tp_sl' ? '🎯' : 
                               notification.activity_type === 'limit_order' ? '📊' : 
                               notification.activity_type === 'swap' ? '🔄' : 
                               notification.activity_type === 'trade' ? '💹' : '🔔'}
                            </div>
                            
                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <p className="text-white text-sm font-medium leading-tight">
                                  {notification.message}
                                </p>
                                {!notification.read && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleMarkAsRead(notification.id);
                                    }}
                                    className="text-gray-400 hover:text-white transition-colors p-1 flex-shrink-0"
                                    title="Mark as read"
                                  >
                                    <FaCheck className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                              
                              {/* Metadata */}
                              <div className="mt-1 flex items-center gap-2 text-xs text-gray-400">
                                {notification.token_symbol && (
                                  <span className="bg-gray-700 px-2 py-1 rounded">
                                    {notification.token_symbol}
                                  </span>
                                )}
                                {notification.exchange_name && (
                                  <span className="bg-gray-700 px-2 py-1 rounded">
                                    {notification.exchange_name}
                                  </span>
                                )}
                                <span>{formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}</span>
                              </div>
                              
                              {/* Amount and Price */}
                              {(notification.amount || notification.price) && (
                                <div className="mt-1 text-xs text-gray-300">
                                  {notification.amount && (
                                    <span>Amount: {notification.amount.toLocaleString()}</span>
                                  )}
                                  {notification.amount && notification.price && <span className="mx-2">•</span>}
                                  {notification.price && (
                                    <span>Price: ${notification.price.toFixed(6)}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Footer */}
                {notifications.length > 0 && (
                  <div className="p-3 border-t border-gray-700">
                    <button
                      onClick={() => setIsNotificationOpen(false)}
                      className="w-full text-center text-sm text-gray-400 hover:text-white transition-colors"
                    >
                      Close notifications
                    </button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Show all icons on large screens */}
      <div className="hidden lg:flex items-center gap-4">
        <SimpleNavbarMonitor />
        <img src={HelpIcon} alt="Help" className="w-8 h-8" />
        <img src={DocumentsIcon} alt="Documents" className="w-8 h-8" />
        <img src={Languages} alt="Languages" className="w-8 h-8" />
        <SettingsDropdown
          onRecoveryKeyClick={() => setIsRecoveryKeyModalOpen(true)}
          onImportWalletClick={() => setIsImportWalletModalOpen(true)}
          onManageWalletsClick={() => setIsManageWalletsModalOpen(true)}
        />
        {/* Wallet Icon with Dropdown */}
        <div className="relative" ref={walletDropdownRef}>
          <button
            className="flex items-center justify-center w-8 h-8 rounded-full bg-[#181C20] hover:bg-[#252A30] transition-colors"
            onClick={() => {
              if (authenticated) {
                const newState = !isWalletDropdownOpen;
                setIsWalletDropdownOpen(newState);
                if (newState) {
                  // Fetch token balances when opening the dropdown
                  fetchTokenBalances();
                }
              }
            }}
          >
            <BiWallet className="text-white" size={20} />
            {/* Token count badge */}
            {tokenBalances.length > 0 && (
              <div className="absolute -top-1 -right-1 bg-[#1D1F24] text-gray-300 text-[9px] font-medium rounded-full w-5 h-5 flex items-center justify-center border border-gray-700 px-1">
                {tokenBalances.length}
              </div>
            )}
          </button>
          
          {/* Wallet Dropdown */}
          {isWalletDropdownOpen && authenticated && (
            <div className="absolute right-0 mt-2 w-80 bg-[#101114] rounded-xl shadow-lg py-2 z-[999] text-white">
              <div className="px-4 py-3 border-b border-gray-700 flex justify-between items-center">
                <p className="text-white text-base font-medium">Token Balances</p>
                {/* Chain indicator */}
                <div className="flex space-x-2">
                  <div className="px-2 py-1 rounded-md text-xs bg-blue-600 text-white flex items-center space-x-1">
                    <img 
                      src="https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png" 
                      alt="SOL" 
                      className="w-3 h-3 rounded-full" 
                    />
                    <span>
                      SOL
                      {loadingBalances ? (
                        <span className="opacity-70 ml-1">...</span>
                      ) : (
                        solBalance !== null && (
                          <span className="ml-1">
                            {solBalance.toFixed(9)}
                          </span>
                        )
                      )}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Search input */}
              <div className="px-4 py-2 border-b border-gray-700">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search tokens..."
                    value={tokenSearch}
                    onChange={(e) => setTokenSearch(e.target.value)}
                    className="w-full bg-[#1A1C24] text-sm text-white px-3 py-2 rounded-md outline-none focus:ring-1 focus:ring-blue-500 pl-8"
                  />
                  <FaSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs" />
                </div>
              </div>
              
              <div className="max-h-72 overflow-y-auto py-1">
                {loadingBalances ? (
                  <div className="text-center py-4 flex flex-col items-center justify-center">
                    <div className="w-8 h-8 border-2 border-t-blue-500 border-r-transparent border-b-blue-500 border-l-transparent rounded-full animate-spin mb-2"></div>
                    <p className="text-sm text-gray-400">Loading balances...</p>
                  </div>
                ) : filteredTokenBalances.length > 0 ? (
                  filteredTokenBalances.map((token, i) => (
                    <div 
                      key={i} 
                      className={`flex items-center justify-between px-4 py-2 hover:bg-[#1E1F24] relative ${token.symbol !== 'SOL' ? 'group cursor-pointer' : ''}`}
                      onClick={() => token.symbol !== 'SOL' && token && handleTokenClick(token)}
                    >
                      <div className="flex items-center space-x-2">
                        {token.logo && (
                          <img src={token.logo} alt={token.symbol} className="w-6 h-6 rounded-full" />
                        )}
                        <div>
                          <p className="text-sm text-white">{token.symbol}</p>
                          <p className="text-xs text-gray-400">{token.name}</p>
                        </div>
                      </div>
                      
                      {token.symbol === 'SOL' ? (
                        // Regular display for SOL - no hover effects
                        <div className="min-w-[80px] text-right">
                          <p className="text-sm text-white">
                            {formatTokenBalance(token.balance, token.decimals)}
                          </p>
                        </div>
                      ) : (
                        // Special hover effects for non-SOL tokens
                        <div className="flex flex-col items-end relative min-w-[90px] h-[30px]">
                          {/* Balance container with extra padding to ensure visibility */}
                          <div className="relative h-full w-full">
                            <p className="text-sm text-white absolute right-0 transition-all duration-300 group-hover:translate-y-6 group-hover:opacity-80 group-hover:text-xs">
                              {formatTokenBalance(token.balance, token.decimals)}
                            </p>
                          </div>
                          
                          {/* Sell All button that appears on hover */}
                          <button 
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent triggering the parent div's onClick
                              console.log("Sell All button clicked for token:", token);
                              if (token) {
                                handleSellAll(token);
                              }
                            }}
                            className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-all duration-200 
                                      px-3 py-0.5 rounded text-xs text-white font-medium w-[60px] whitespace-nowrap
                                      bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700
                                      overflow-hidden"
                          >
                            <span className="absolute inset-0 w-full h-full bg-white/20 animate-shine"></span>
                            <span className="relative z-10">Sell All</span>
                          </button>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-400">No token balances found</p>
                  </div>
                )}
              </div>
              
              <div className="px-4 py-3 border-t border-gray-800 mt-1">
                <button
                  onClick={() => {
                    setIsManageWalletsModalOpen(true);
                    setIsWalletDropdownOpen(false);
                  }}
                  className="w-full text-center text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  Manage Wallets
                </button>
              </div>
            </div>
          )}
        </div>
        <div className="relative" ref={notificationDropdownRef}>
          <button
            onClick={handleNotificationClick}
            className="relative flex items-center justify-center w-8 h-8 rounded-lg hover:bg-[#1D2226] transition-colors"
          >
            <img src={NotificationIcon} alt="Notification" className="w-8 h-8" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] text-center leading-none">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>
          
          {/* Notification Dropdown */}
          {isNotificationOpen && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-[998]" 
                onClick={() => setIsNotificationOpen(false)}
              />
              
              {/* Dropdown */}
              <div className="absolute right-0 mt-2 w-96 bg-[#101114] rounded-xl shadow-lg border border-gray-700 z-[999] max-h-[500px] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700">
                  <div className="flex items-center gap-2">
                    <img src={NotificationIcon} alt="Notifications" className="w-5 h-5" />
                    <h3 className="text-white font-semibold">Notifications</h3>
                    {unreadCount > 0 && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                        {unreadCount}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {unreadCount > 0 && (
                      <button
                        onClick={handleMarkAllAsRead}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                        title="Mark all as read"
                      >
                        <FaCheck className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => setIsNotificationOpen(false)}
                      className="text-gray-400 hover:text-white transition-colors p-1"
                    >
                      <FaTimes className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto">
                  {loadingNotifications ? (
                    <div className="p-4 text-center text-gray-400">
                      <div className="animate-spin w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full mx-auto mb-2"></div>
                      Loading notifications...
                    </div>
                  ) : !notifications || notifications.length === 0 ? (
                    <div className="p-8 text-center text-gray-400">
                      <img src={NotificationIcon} alt="No notifications" className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p className="text-lg font-medium mb-1">No notifications yet</p>
                      <p className="text-sm">You'll see your trading activity and updates here</p>
                    </div>
                  ) : (
                    <div className="py-2">
                      {(notifications || []).map((notification) => (
                        <div
                          key={notification.id}
                          className={`relative px-4 py-3 hover:bg-[#1A1D21] border-b border-gray-800 last:border-b-0 cursor-pointer ${
                            !notification.read ? 'bg-[#1A1D21]/50' : ''
                          }`}
                          onClick={() => {
                            if (!notification.read) {
                              handleMarkAsRead(notification.id);
                            }
                          }}
                        >
                          {/* Unread indicator */}
                          {!notification.read && (
                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                          
                          <div className="flex items-start gap-3 ml-2">
                            {/* Icon */}
                            <div className="text-2xl mt-1">
                              {notification.activity_type === 'tp_sl' ? '🎯' : 
                               notification.activity_type === 'limit_order' ? '📊' : 
                               notification.activity_type === 'swap' ? '🔄' : 
                               notification.activity_type === 'trade' ? '💹' : '🔔'}
                            </div>
                            
                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <p className="text-white text-sm font-medium leading-tight">
                                  {notification.message}
                                </p>
                                {!notification.read && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleMarkAsRead(notification.id);
                                    }}
                                    className="text-gray-400 hover:text-white transition-colors p-1 flex-shrink-0"
                                    title="Mark as read"
                                  >
                                    <FaCheck className="w-3 h-3" />
                                  </button>
                                )}
                              </div>
                              
                              {/* Metadata */}
                              <div className="mt-1 flex items-center gap-2 text-xs text-gray-400">
                                {notification.token_symbol && (
                                  <span className="bg-gray-700 px-2 py-1 rounded">
                                    {notification.token_symbol}
                                  </span>
                                )}
                                {notification.exchange_name && (
                                  <span className="bg-gray-700 px-2 py-1 rounded">
                                    {notification.exchange_name}
                                  </span>
                                )}
                                <span>{formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}</span>
                              </div>
                              
                              {/* Amount and Price */}
                              {(notification.amount || notification.price) && (
                                <div className="mt-1 text-xs text-gray-300">
                                  {notification.amount && (
                                    <span>Amount: {notification.amount.toLocaleString()}</span>
                                  )}
                                  {notification.amount && notification.price && <span className="mx-2">•</span>}
                                  {notification.price && (
                                    <span>Price: ${notification.price.toFixed(6)}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Footer */}
                {notifications.length > 0 && (
                  <div className="p-3 border-t border-gray-700">
                    <button
                      onClick={() => setIsNotificationOpen(false)}
                      className="w-full text-center text-sm text-gray-400 hover:text-white transition-colors"
                    >
                      Close notifications
                    </button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* Profile Section with Dropdown */}
      <div className="relative" data-profile-menu>
        <button 
          onClick={() => setIsProfileOpen(!isProfileOpen)}
          className="flex items-center gap-2 p-2 rounded-lg hover:bg-[#1D2226] transition-colors"
        >
          <img src={ProfileIcon} alt="Profile" className="w-8 h-8" />
        </button>

        {/* Profile Dropdown */}
        {isProfileOpen && authenticated && (
          <div className="absolute right-0 mt-2 w-64 bg-[#101114] rounded-xl shadow-lg py-2 z-[999] text-white">
            <div className="px-4 py-3 border-b border-gray-700">
              <p className="text-white text-base font-medium">{userInfo.displayName}</p>
              {userInfo.email && typeof userInfo.email === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userInfo.email}</p>
              ) : userInfo.userId && typeof userInfo.userId === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userInfo.userId}</p>
              ) : userAddress && typeof userAddress === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userAddress}</p>
              ) : null}
            </div>

            {/* Display Solana wallet addresses */}
            <div className="max-h-60 overflow-y-auto py-1">
              {solanaAddresses.length > 0 ? (
                <div>
                  <p className="text-sm text-gray-400 px-4 py-2">Solana Wallets</p>
                  {solanaAddresses.map((addr, i) => {
                    // Find SOL token balance for this address
                    const solToken = tokenBalances.find(token => token.symbol === 'SOL');
                    const solBalance = solToken ? formatTokenBalance(solToken.balance, solToken.decimals) : null;
                    
                    return (
                      <div key={i} className="px-4 py-2 hover:bg-[#1E1F24]">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-white truncate w-32">{formatAddress(addr.address)}</span>
                            {addr.walletType === 'smart' && (
                              <span className="text-xs px-1.5 py-0.5 rounded-md bg-green-900/30 text-green-400">Smart</span>
                            )}
                          </div>
                          <button
                            onClick={() => copyToClipboard(addr.address)}
                            className="p-1 text-gray-400 hover:text-white"
                          >
                            {copiedAddress === addr.address ? <FaCheck size={14} /> : <FaCopy size={14} />}
                          </button>
                        </div>
                        
                        {/* Show SOL balance for this wallet */}
                        {solBalance && !loadingBalances ? (
                          <div className="flex items-center space-x-2 mt-1">
                            <img 
                              src="https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png" 
                              alt="SOL" 
                              className="w-4 h-4 rounded-full" 
                            />
                            <span className="text-xs text-blue-400">{solBalance} SOL</span>
                          </div>
                        ) : loadingBalances ? (
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="w-3 h-3 rounded-full border-t-blue-500 border-r-transparent border border-blue-500 animate-spin"></div>
                            <span className="text-xs text-gray-400">Loading balance...</span>
                          </div>
                        ) : null}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="px-4 py-8 text-center text-gray-400">
                  <p>No Solana wallets connected</p>
                </div>
              )}
            </div>

            <div className="px-4 py-3 border-t border-gray-800 mt-1">
              <button
                onClick={() => {
                  logout();
                  setIsProfileOpen(false);
                }}
                className="w-full text-left text-sm text-red-400 hover:text-red-300 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>
    </div>

    {/* Mobile Menu (sliding panel) */}
    {mobileMenuOpen && (
      <div className="lg:hidden fixed inset-0 bg-[#141416] z-[998] flex flex-col pt-16">
        <div className="container mx-auto px-4">
          {/* Search in mobile menu */}
          <div className="relative mb-6">
            <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-[#181C20] text-white pl-12 pr-4 py-3 rounded-3xl w-full outline-none"
              onClick={() => {
                setIsSearchOpen(true);
                setMobileMenuOpen(false);
              }}
              readOnly
            />
          </div>
          
          {/* Navigation links */}
          <ul className="flex flex-col gap-4 mb-8">
            {["Pulse", "Portfolio"].map((tab) => (
              <li
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-3 rounded-xl font-medium cursor-pointer transition-all ${
                  activeTab === tab ? "bg-white text-gray-600" : "text-white"
                }`}
              >
                {tab}
              </li>
            ))}
          </ul>

          {/* Icons in mobile menu */}
          <div className="grid grid-cols-3 gap-6">
            <div className="flex flex-col items-center gap-2">
              <img src={HelpIcon} alt="Help" className="w-10 h-10" />
              <span className="text-sm">Help</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={DocumentsIcon} alt="Documents" className="w-10 h-10" />
              <span className="text-sm">Docs</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={Languages} alt="Languages" className="w-10 h-10" />
              <span className="text-sm">Language</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={SettingsIcon} alt="Settings" className="w-4 h-4" />
              
              <span className="text-sm">Settings</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <button
                onClick={handleNotificationClick}
                className="relative flex items-center justify-center"
              >
                <img src={NotificationIcon} alt="Notifications" className="w-10 h-10" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] text-center leading-none">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>
                )}
              </button>
              <span className="text-sm">Alerts ({unreadCount})</span>
            </div>
            {authenticated && (
              <div className="flex flex-col items-center gap-2">
                <button
                  onClick={() => {
                    logout();
                    setMobileMenuOpen(false);
                  }}
                  className="w-10 h-10 flex items-center justify-center bg-red-500/20 rounded-full"
                >
                  <FaTimes className="text-red-400" />
                </button>
                <span className="text-sm text-red-400">Sign Out</span>
              </div>
            )}
          </div>
        </div>
      </div>
    )}

    {isSearchOpen && (
      <SearchModal
        onClose={() => {
          setIsSearchOpen(false);
          setMobileMenuOpen(false);
        }}
      />
    )}

    {/* Recovery Key Modal */}
    <RecoveryKeyModal
      isOpen={isRecoveryKeyModalOpen}
      onClose={() => setIsRecoveryKeyModalOpen(false)}
    />

    {/* Import Wallet Modal */}
    <ImportWalletModal
      isOpen={isImportWalletModalOpen}
      onClose={() => setIsImportWalletModalOpen(false)}
    />

    {/* Manage Wallets Modal */}
    <ManageWalletsModal
      isOpen={isManageWalletsModalOpen}
      onClose={() => setIsManageWalletsModalOpen(false)}
    />
  </nav>
  );
};

export default Navbar;



