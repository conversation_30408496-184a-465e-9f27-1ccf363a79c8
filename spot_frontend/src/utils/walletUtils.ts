/**
 * Utility functions for wallet management
 * Provides consistent wallet access across components using defaultWallets localStorage
 */

import { getCachedWalletId, getPrivySolanaWalletInfo, getPrivySolanaWalletInfoWithDelegation } from '../api/privy_api';

/**
 * Get the default Solana wallet address from localStorage
 * Falls back to Privy hooks if no default wallet is set
 * 
 * @param authenticated - Whether user is authenticated
 * @param solanaWallets - Array of Solana wallets from Privy hooks
 * @returns Solana wallet address or null
 */
export const getDefaultSolanaWalletAddress = (
  authenticated: boolean,
  solanaWallets: any[]
): string | null => {
  if (!authenticated) {
    console.log('User not authenticated');
    return null;
  }

  // First try to get from defaultWallets localStorage
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      if (defaultWallets.solana) {

        return defaultWallets.solana;
      }
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }

  // Fallback to Privy hooks if no default wallet set
  if (solanaWallets.length === 0) {
    console.log('No Solana wallets connected');
    return null;
  }

  const solanaWallet = solanaWallets[0];
  if (!solanaWallet.address) {
    console.log('Solana wallet found but no address available');
    return null;
  }

  console.log('Using fallback Solana wallet from Privy:', solanaWallet.address);
  return solanaWallet.address;
};

/**
 * Get Solana wallet info (address and ID) for swap operations
 * Uses defaultWallets localStorage for consistent wallet selection
 * 
 * @param authenticated - Whether user is authenticated
 * @param userId - User ID from Privy
 * @param solanaWallets - Array of Solana wallets from Privy hooks
 * @param user - User object from Privy (optional)
 * @returns Promise<{address: string, id: string} | null>
 */
export const getDefaultSolanaWalletInfo = async (
  authenticated: boolean,
  userId: string | undefined,
  solanaWallets: any[],
  user?: any
): Promise<{ address: string; id: string } | null> => {
  if (!authenticated || !userId) {
    console.log('User not authenticated or user ID not available');
    return null;
  }

  // First try to get wallet address from defaultWallets localStorage
  let walletAddress: string | null = null;
  let isDefaultFromStorage = false;
  
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      if (defaultWallets.solana) {
        walletAddress = defaultWallets.solana;
        isDefaultFromStorage = true;
        console.log('Found default Solana wallet in localStorage:', walletAddress);
      }
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }

  // Fallback to Privy hooks if no default wallet set
  if (!walletAddress) {
    if (solanaWallets.length === 0) {
      console.log('No Solana wallets connected');
      return null;
    }

    const solanaWallet = solanaWallets[0];
    if (!solanaWallet.address) {
      console.log('Solana wallet found but no address available');
      return null;
    }

    walletAddress = solanaWallet.address;
    console.log('Using fallback Solana wallet from Privy for swap:', walletAddress);
  }

  console.log('Getting wallet info for swap:', {
    address: walletAddress,
    source: walletAddress === solanaWallets[0]?.address ? 'privy' : 'localStorage'
  });

  // 1. Try to get cached wallet ID first (fastest)
  const cachedWalletId = getCachedWalletId(userId, walletAddress);
  if (cachedWalletId) {
    console.log('Using cached wallet ID:', cachedWalletId);
    return {
      address: walletAddress,
      id: cachedWalletId
    };
  }

  // 2. If no cache, try Privy API (should be rare if preloading works)
  try {
    console.log('Cache miss, fetching from Privy API...');
    // Force refresh to ensure we get latest data
    const privyResponse = await getPrivySolanaWalletInfo(userId, true);

    if (privyResponse.success && privyResponse.data) {
      console.log('Privy API response:', {
        primaryAddress: privyResponse.data.address,
        primaryWalletId: privyResponse.data.walletId,
        totalWallets: privyResponse.data.totalSolanaWallets,
        allWallets: privyResponse.data.allSolanaWallets
      });
      
      // Check if the connected wallet matches the Privy wallet
      if (privyResponse.data.address === walletAddress) {
        console.log('Found matching wallet ID from Privy API:', privyResponse.data.walletId);
        return {
          address: walletAddress,
          id: privyResponse.data.walletId
        };
      } else {
        // Try to find matching wallet in all Solana wallets
        const matchingWallet = privyResponse.data.allSolanaWallets.find(
          wallet => wallet.address === walletAddress
        );

        if (matchingWallet) {
          console.log('Found matching wallet ID from Privy API (multiple wallets):', matchingWallet.id);
          return {
            address: walletAddress,
            id: matchingWallet.id
          };
        } else {
          console.warn('No matching wallet found in Privy API response for address:', walletAddress);
          
          // If the localStorage wallet doesn't match any Privy wallet, 
          // try using the primary wallet from Privy instead
          if (isDefaultFromStorage && privyResponse.data.allSolanaWallets.length > 0) {
            console.log('localStorage wallet not found in Privy, using primary wallet instead');
            const primaryWallet = privyResponse.data.allSolanaWallets[0];
            
            // Update localStorage with the correct wallet
            try {
              const storedDefaults = localStorage.getItem('defaultWallets') || '{}';
              const defaultWallets = JSON.parse(storedDefaults);
              defaultWallets.solana = primaryWallet.address;
              localStorage.setItem('defaultWallets', JSON.stringify(defaultWallets));
              console.log('Updated defaultWallets in localStorage with primary Privy wallet');
            } catch (e) {
              console.error('Error updating defaultWallets:', e);
            }
            
            return {
              address: primaryWallet.address,
              id: primaryWallet.id
            };
          }
        }
      }
    } else {
      console.warn('Privy API returned unsuccessful response:', privyResponse);
    }
  } catch (error) {
    console.error('Error fetching wallet ID from Privy API:', error);
  }

  // 3. Try to find the wallet ID from the user's linkedAccounts
  if (user?.linkedAccounts) {
    const linkedWallet = user.linkedAccounts.find((account: any) => {
      return account.type === 'wallet' && 
             account.address === walletAddress &&
             account.walletClientType === 'solana';
    });
    
    // Check for either 'id' or 'walletId' property
    const walletId = linkedWallet?.id || linkedWallet?.walletId;
    if (walletId) {
      console.log('Found wallet ID from linkedAccounts:', walletId);
      return {
        address: walletAddress,
        id: walletId
      };
    }
  }

  // 4. If we still can't find a wallet ID, we should not proceed with a fake one
  console.error('Could not find valid wallet ID for address:', walletAddress);
  console.log('Debug info:', {
    walletAddress,
    solanaWallets: solanaWallets.map(w => ({ 
      address: w.address, 
      walletClientType: w.walletClientType 
    })),
    linkedAccounts: user?.linkedAccounts?.filter((acc: any) => acc.type === 'wallet')
  });
  
  // Return null to indicate failure - the calling code should handle this
  return null;
};

/**
 * Interface for default wallets stored in localStorage
 */
export interface DefaultWallets {
  ethereum?: string;
  solana?: string;
}

/**
 * Get all default wallets from localStorage
 * 
 * @returns DefaultWallets object or empty object if none found
 */
export const getDefaultWallets = (): DefaultWallets => {
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      return JSON.parse(storedDefaults);
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }
  return {};
};

/**
 * Set default wallets in localStorage
 * 
 * @param wallets - DefaultWallets object to store
 */
export const setDefaultWallets = (wallets: DefaultWallets): void => {
  try {
    localStorage.setItem('defaultWallets', JSON.stringify(wallets));
    console.log('Default wallets updated:', wallets);
    
    // Dispatch a custom event to notify components of the change
    window.dispatchEvent(new CustomEvent('defaultWalletsChanged', {
      detail: wallets
    }));
  } catch (error) {
    console.error('Error storing defaultWallets to localStorage:', error);
  }
};

/**
 * Clear wallet cache to force fresh fetch from Privy API
 * Useful when wallet connections change
 */
export const clearWalletInfoCache = (): void => {
  try {
    localStorage.removeItem('privy_solana_wallet_cache');
    console.log('Wallet info cache cleared');
  } catch (error) {
    console.error('Error clearing wallet cache:', error);
  }
};

/**
 * Get Solana wallet info with automatic delegation handling
 * This version will trigger delegation if needed
 * 
 * @param authenticated - Whether user is authenticated
 * @param userId - User ID from Privy
 * @param solanaWallets - Array of Solana wallets from Privy hooks
 * @param user - User object from Privy (optional)
 * @param onDelegationNeeded - Callback to handle wallet delegation
 * @returns Promise<{address: string, id: string} | null>
 */
export const getDefaultSolanaWalletInfoWithDelegation = async (
  authenticated: boolean,
  userId: string | undefined,
  solanaWallets: any[],
  user?: any,
  onDelegationNeeded?: (address: string) => Promise<boolean>
): Promise<{ address: string; id: string } | null> => {
  if (!authenticated || !userId) {
    console.log('User not authenticated or user ID not available');
    return null;
  }

  // First try to get wallet address from defaultWallets localStorage
  let walletAddress: string | null = null;
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      if (defaultWallets.solana) {
        walletAddress = defaultWallets.solana;
        console.log('Found default Solana wallet in localStorage:', walletAddress);
      }
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }

  // Fallback to Privy hooks if no default wallet set
  if (!walletAddress) {
    if (solanaWallets.length === 0) {
      console.log('No Solana wallets connected');
      return null;
    }

    const solanaWallet = solanaWallets[0];
    if (!solanaWallet.address) {
      console.log('Solana wallet found but no address available');
      return null;
    }

    walletAddress = solanaWallet.address;
    console.log('Using fallback Solana wallet from Privy for swap:', walletAddress);
  }

  try {
    // Use the delegation-aware API
    const privyResponse = await getPrivySolanaWalletInfoWithDelegation(
      userId,
      walletAddress,
      onDelegationNeeded
    );

    if (privyResponse.success && privyResponse.data) {
      // If the returned wallet is different from requested (due to delegation issues)
      if (privyResponse.data.address !== walletAddress) {
        console.log('Wallet changed from', walletAddress, 'to', privyResponse.data.address);
        
        // Update localStorage with the working wallet
        try {
          const storedDefaults = localStorage.getItem('defaultWallets') || '{}';
          const defaultWallets = JSON.parse(storedDefaults);
          defaultWallets.solana = privyResponse.data.address;
          localStorage.setItem('defaultWallets', JSON.stringify(defaultWallets));
          console.log('Updated defaultWallets with delegated wallet');
        } catch (e) {
          console.error('Error updating defaultWallets:', e);
        }
      }
      
      return {
        address: privyResponse.data.address,
        id: privyResponse.data.walletId
      };
    }
  } catch (error) {
    console.error('Error getting wallet info with delegation:', error);
  }

  // If all else fails, return null
  return null;
};
