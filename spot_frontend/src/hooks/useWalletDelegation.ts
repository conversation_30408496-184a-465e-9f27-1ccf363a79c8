/**
 * React hook for wallet delegation management
 * Provides easy-to-use interface for checking and managing wallet delegation
 */

import { useState, useCallback, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useSessionSigners } from '../utils/sessionSigners';
import {
  checkWalletDelegationStatus,
  handleWalletDelegationWorkflow,
  getStoredDefaultWalletId,
  updateStoredDefaultWallet,
  WalletDelegationStatus,
  DelegationCheckResult
} from '../utils/walletDelegationManager';

export interface UseWalletDelegationResult {
  // State
  isChecking: boolean;
  delegationStatus: DelegationCheckResult | null;
  currentWallet: WalletDelegationStatus | null;
  error: string | null;
  
  // Actions
  checkDelegationStatus: () => Promise<DelegationCheckResult | null>;
  handleDelegationWorkflow: () => Promise<WalletDelegationStatus | null>;
  switchToWallet: (wallet: WalletDelegationStatus) => void;
  clearError: () => void;
  
  // Computed properties
  needsDelegation: boolean;
  hasValidDelegatedWallet: boolean;
  recommendedWallet: WalletDelegationStatus | null;
}

/**
 * Hook for managing wallet delegation
 */
export const useWalletDelegation = (): UseWalletDelegationResult => {
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets, createWallet } = useSolanaWallets();
  const { delegateSolanaWallet } = useSessionSigners();
  
  const [isChecking, setIsChecking] = useState(false);
  const [delegationStatus, setDelegationStatus] = useState<DelegationCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Check current delegation status
   */
  const checkDelegationStatus = useCallback(async (): Promise<DelegationCheckResult | null> => {
    if (!authenticated || !user?.id) {
      setError('User not authenticated');
      return null;
    }

    setIsChecking(true);
    setError(null);

    try {
      const storedWalletAddress = getStoredDefaultWalletId();
      const result = await checkWalletDelegationStatus(user.id, storedWalletAddress || undefined);
      
      setDelegationStatus(result);
      
      if (!result.success) {
        setError(result.message || 'Failed to check delegation status');
        return null;
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error checking delegation status';
      setError(errorMessage);
      console.error('Error checking delegation status:', err);
      return null;
    } finally {
      setIsChecking(false);
    }
  }, [authenticated, user?.id]);

  /**
   * Handle the complete delegation workflow with user prompts
   */
  const handleDelegationWorkflow = useCallback(async (): Promise<WalletDelegationStatus | null> => {
    if (!authenticated || !user?.id) {
      setError('User not authenticated');
      return null;
    }

    setIsChecking(true);
    setError(null);

    try {
      const result = await handleWalletDelegationWorkflow(
        user.id,
        delegateSolanaWallet,
        (wallet) => {
          console.log('✅ Delegation workflow successful:', wallet);
          // Refresh delegation status
          checkDelegationStatus();
        },
        (errorMessage) => {
          console.error('❌ Delegation workflow error:', errorMessage);
          setError(errorMessage);
        },
        // Wallet creation callback
        async () => {
          console.log('🔄 Creating new embedded wallet...');
          try {
            const newWallet = await createWallet();
            if (newWallet && newWallet.address) {
              // Enable delegation for the new wallet
              const delegationResult = await delegateSolanaWallet(newWallet.address);

              const walletStatus: WalletDelegationStatus = {
                walletId: newWallet.id || `wallet_${Date.now()}`,
                address: newWallet.address,
                delegated: delegationResult === true,
                imported: false,
                verifiedAt: Date.now()
              };

              console.log('✅ New embedded wallet created with delegation:', walletStatus);
              return walletStatus;
            }
            return null;
          } catch (error) {
            console.error('❌ Error creating embedded wallet:', error);
            setError(error instanceof Error ? error.message : 'Failed to create wallet');
            return null;
          }
        }
      );

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error in delegation workflow';
      setError(errorMessage);
      console.error('Error in delegation workflow:', err);
      return null;
    } finally {
      setIsChecking(false);
    }
  }, [authenticated, user?.id, delegateSolanaWallet, checkDelegationStatus]);

  /**
   * Switch to a specific wallet
   */
  const switchToWallet = useCallback((wallet: WalletDelegationStatus) => {
    updateStoredDefaultWallet(wallet.address);
    
    // Update delegation status to reflect the change
    if (delegationStatus) {
      setDelegationStatus({
        ...delegationStatus,
        currentWallet: wallet
      });
    }
    
    console.log('🔄 Switched to wallet:', wallet.address);
  }, [delegationStatus]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Auto-check delegation status when user changes
   */
  useEffect(() => {
    if (authenticated && user?.id) {
      checkDelegationStatus();
    }
  }, [authenticated, user?.id, checkDelegationStatus]);

  // Computed properties
  const currentWallet = delegationStatus?.currentWallet || null;
  const needsDelegation = delegationStatus?.needsDelegation || false;
  const hasValidDelegatedWallet = delegationStatus?.hasValidDelegatedWallet || false;
  const recommendedWallet = delegationStatus?.recommendedWallet || null;

  return {
    // State
    isChecking,
    delegationStatus,
    currentWallet,
    error,
    
    // Actions
    checkDelegationStatus,
    handleDelegationWorkflow,
    switchToWallet,
    clearError,
    
    // Computed properties
    needsDelegation,
    hasValidDelegatedWallet,
    recommendedWallet
  };
};

/**
 * Hook for automatic delegation management
 * This version automatically handles delegation workflow on mount
 */
export const useAutoWalletDelegation = (autoTrigger: boolean = true) => {
  const delegation = useWalletDelegation();
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    if (autoTrigger && !hasTriggered && delegation.needsDelegation && !delegation.isChecking) {
      setHasTriggered(true);
      delegation.handleDelegationWorkflow();
    }
  }, [autoTrigger, hasTriggered, delegation.needsDelegation, delegation.isChecking, delegation.handleDelegationWorkflow]);

  return delegation;
};
