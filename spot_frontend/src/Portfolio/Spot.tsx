import React, { useState, useEffect } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
  Tooltip,
  Filler,
} from "chart.js";
import EmptyState from "./components/EmptyState";
import MetricCard from "./components/MetricCard";
import { useWallets, usePrivy } from "@privy-io/react-auth";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { tokenBalanceCache } from "../utils/tokenBalanceCache";
import { getDefaultSolanaWalletAddress } from "../utils/getDefaultWallet";
import { useUserTradeData } from "../hooks/useUserTradeData";

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement, Tooltip, Filler);

const dummyChartData = {
  labels: Array(30).fill(""),
  datasets: [
    {
      data: Array(30).fill(0).map((_, i) => Math.sin(i * 0.3) * 50 + 100 + Math.random() * 20),
      borderColor: "#7FFFD4",
      backgroundColor: "rgba(127, 255, 212, 0.1)",
      borderWidth: 2,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 6,
      pointHoverBackgroundColor: "#7FFFD4",
      fill: true,
    },
  ],
};

interface TokenBalance {
  mintAddress: string;
  symbol?: string;
  name?: string;
  balance: string;
  decimals: number;
  tokenStandard?: string;
  logo?: string;
  uri?: string;
  description?: string;
  website?: string;
  twitter?: string;
  discord?: string;
  price?: number;
  value?: number;
}

interface UserTrade {
  id: string;
  timestamp: string;
  type: 'buy' | 'sell';
  tokenSymbol: string;
  tokenMint: string;
  amount: number;
  price: number;
  total: number;
  walletAddress: string;
}

const PortfolioDashboard = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState("24H");
  const [activeView, setActiveView] = useState<"Assets" | "Trades">("Assets");
  const [assets, setAssets] = useState<TokenBalance[]>([]);
  const [loading, setLoading] = useState(false);
  const [assetsLoading, setAssetsLoading] = useState(false);
  const [assetError, setAssetError] = useState<string | null>(null);
  const [hasFetchedAssets, setHasFetchedAssets] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [assetsPage, setAssetsPage] = useState(1);
  const [tradesPage, setTradesPage] = useState(1);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [balanceSortOrder, setBalanceSortOrder] = useState<"asc" | "desc" | null>(null);
  const [filterByToken, setFilterByToken] = useState<string | null>(null);
  
  const navigate = useNavigate();
  const { wallets } = useWallets();
  const { user } = usePrivy();
  
  const itemsPerPage = 10;
  
  // Get Solana wallet address - try useWallets first, then linkedAccounts
  let walletAddress: string | undefined;
  
  // First try to get from useWallets hook
  const solanaWallet = wallets.find(wallet => wallet.walletClientType === 'solana');
  if (solanaWallet?.address) {
    walletAddress = solanaWallet.address;
    console.log('Got Solana wallet from useWallets:', walletAddress);
  } else if (user?.linkedAccounts) {
    // Fallback to linkedAccounts like Navbar does
    const solanaAccount = user.linkedAccounts.find(
      account => account.type === 'wallet' && 
      (account as any).walletClientType === 'solana' &&
      (account as any).address
    );
    if (solanaAccount && (solanaAccount as any).address) {
      walletAddress = (solanaAccount as any).address;
      console.log('Got Solana wallet from linkedAccounts:', walletAddress);
    }
  }
  
  // Final fallback to localStorage
  if (!walletAddress) {
    const defaultAddress = getDefaultSolanaWalletAddress();
    if (defaultAddress) {
      walletAddress = defaultAddress;
      console.log('Got Solana wallet from localStorage:', walletAddress);
    }
  }
  
  console.log('Final wallet address:', walletAddress);
  
  // Use the useUserTradeData hook for fetching user trades
  const userTradeData = useUserTradeData(
    activeView === "Trades" ? walletAddress : null,
    filterByToken
  );
  
  // Process token data helper function
  const processTokenData = (tokenData: any) => {
    console.log('Processing token data:', tokenData);
    
    if (tokenData.tokens && Array.isArray(tokenData.tokens)) {
      console.log(`Found ${tokenData.tokens.length} tokens`);
      let allAssets = [...tokenData.tokens];
      
      // Add SOL balance as the first item if it exists
      if (tokenData.sol) {
        console.log('SOL data:', tokenData.sol);
        // SOL balance might come as sol.sol or sol.balance
        let solBalance = '0';
        if (typeof tokenData.sol.sol === 'number') {
          solBalance = tokenData.sol.sol.toString();
        } else if (tokenData.sol.balance) {
          solBalance = tokenData.sol.balance;
        }
        
        const solAsset: TokenBalance = {
          mintAddress: 'So11111111111111111111111111111111111111112',
          symbol: 'SOL',
          name: 'Solana',
          balance: solBalance,
          decimals: 9,
          logo: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
          price: tokenData.sol.price,
          value: tokenData.sol.value || (parseFloat(solBalance) * (tokenData.sol.price || 0))
        };
        allAssets = [solAsset, ...allAssets];
      }
      
      console.log('Setting assets state with:', allAssets);
      setAssets(allAssets);
      setHasFetchedAssets(true);
    } else {
      console.log('No tokens array found');
      setAssets([]);
      setHasFetchedAssets(true);
    }
  };
  
  // Fetch assets data
  useEffect(() => {
    // Prevent API call if conditions not met
    if (!walletAddress || activeView !== "Assets") {
      console.log('Skipping assets fetch:', { 
        noWallet: !walletAddress, 
        wrongView: activeView !== "Assets"
      });
      return;
    }
    
    let abortController = new AbortController();
    let isMounted = true;
    
    const fetchAssets = async () => {
      // Skip if already loading or already fetched
      if (assetsLoading || hasFetchedAssets) {
        console.log('Skipping assets fetch - already loading or fetched');
        return;
      }
      
      setAssetsLoading(true);
      setAssetError(null);
      
      try {
        console.log('Fetching assets for wallet:', walletAddress);
        
        // Check cache first
        const cachedData = tokenBalanceCache.get(walletAddress);
        if (cachedData) {
          console.log('Using cached data for wallet:', walletAddress);
          // Process cached data
          if (cachedData.success && cachedData.data && isMounted) {
            const tokenData = cachedData.data;
            processTokenData(tokenData);
          }
          setAssetsLoading(false);
          return;
        }
        
        // Check if there's already a pending request for this wallet
        const pendingRequest = tokenBalanceCache.getPendingRequest(walletAddress);
        if (pendingRequest) {
          console.log('Using pending request for wallet:', walletAddress);
          const response = await pendingRequest;
          // Process the response
          if (response.data?.success && response.data?.data && isMounted) {
            processTokenData(response.data.data);
          }
          setAssetsLoading(false);
          return;
        }
        
        // Make the API request and store it as pending
        const requestPromise = axios.post(
          `/api/wallet/solana-token-balance`,
          { walletAddress },
          {
            headers: {
              'Content-Type': 'application/json',
            },
            signal: abortController.signal
          }
        );
        
        tokenBalanceCache.setPendingRequest(walletAddress, requestPromise);
        const response = await requestPromise;
        tokenBalanceCache.clearPendingRequest(walletAddress);
        
        if (!isMounted) return;
        
        console.log('Full API Response:', response.data);
        console.log('Response success:', response.data?.success);
        console.log('Response data field:', response.data?.data);
        
        // The response can have two structures:
        // 1. Controller wraps service response: { success: true, message: "...", data: { success: true/false, tokens: [...], sol: {...} } }
        // 2. Direct service response on error: { success: false, message: "...", data: null }
        
        if (response.data?.success === false && response.data?.data === null) {
          // This is an error response from the controller
          console.log('Controller returned error:', response.data.message);
          throw new Error(response.data.message);
        }
        
        // Cache the successful response
        if (response.data?.success) {
          tokenBalanceCache.set(walletAddress, response.data);
        }
        
        if (response.data?.success && response.data?.data) {
          const tokenData = response.data.data;
          console.log('Token data structure:', tokenData);
          
          // Check if the service itself returned success
          if (tokenData.success === false) {
            console.log('Service returned error:', tokenData.message);
            throw new Error(tokenData.message || 'Failed to fetch token balances');
          }
          
          processTokenData(tokenData);
        } else {
          console.log('Response not successful or missing data field');
          setAssets([]);
          setHasFetchedAssets(true);
        }
      } catch (error: any) {
        if (error.name === 'CanceledError' || !isMounted) {
          console.log('Request was cancelled');
          return;
        }
        
        console.error('Error fetching assets:', error);
        if (error.response?.status === 500 && error.response?.data?.message?.includes('429')) {
          setAssetError('Rate limit exceeded. Please wait a moment and try again.');
          // Retry after 5 seconds for rate limit errors
          setTimeout(() => {
            if (isMounted) {
              setHasFetchedAssets(false);
              setAssetError(null);
            }
          }, 5000);
        } else {
          setAssetError('Failed to load assets. Please try again.');
        }
      } finally {
        if (isMounted) {
          setAssetsLoading(false);
        }
      }
    };

    // Delay to debounce rapid changes
    const timeoutId = setTimeout(() => {
      fetchAssets();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      abortController.abort();
      isMounted = false;
    };
  }, [walletAddress, activeView, refreshTrigger]);


  // Calculate total portfolio value
  const totalValue = assets.reduce((sum, asset) => sum + (asset.value || 0), 0);

  // Filter assets based on search term
  const filteredAssets = assets.filter(asset => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      asset.symbol?.toLowerCase().includes(search) ||
      asset.name?.toLowerCase().includes(search) ||
      asset.mintAddress?.toLowerCase().includes(search)
    );
  });

  // Sort filtered assets based on balance
  const sortedFilteredAssets = [...filteredAssets].sort((a, b) => {
    if (!balanceSortOrder) return 0;
    
    const balanceA = parseFloat(a.balance || '0');
    const balanceB = parseFloat(b.balance || '0');
    
    if (balanceSortOrder === 'asc') {
      return balanceA - balanceB;
    } else {
      return balanceB - balanceA;
    }
  });

  // Transform trades data from hook to match component format
  const trades = userTradeData.trades.map((trade: any) => {
    // The hook already provides formatted data, we just need to map it to our component's format
    const transformedTrade = {
      id: trade.id,
      timestamp: new Date(trade.timestamp).toISOString(),
      type: trade.type,
      tokenSymbol: trade.tokenSymbol || 'Unknown',
      tokenMint: trade.tokenAddress || trade.token1Address,
      amount: trade.actualTokenAmount || 0, // actualTokenAmount is the raw number for token quantity
      price: trade.originalTrade?.price_usd_token1 || trade.originalTrade?.price || 0,
      total: trade.tokenAmount || 0, // tokenAmount is the SOL amount
      walletAddress: trade.trader
    };
    
    // Debug log for first few trades
    if (userTradeData.trades.indexOf(trade) < 3) {
      console.log('Portfolio Spot - Trade data:', {
        original: trade,
        transformed: transformedTrade
      });
    }
    
    return transformedTrade;
  });

  // Filter trades based on search term
  const filteredTrades = trades.filter((trade: any) => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      trade.tokenSymbol?.toLowerCase().includes(search) ||
      trade.type?.toLowerCase().includes(search) ||
      new Date(trade.timestamp).toLocaleDateString().toLowerCase().includes(search)
    );
  });

  // Paginate filtered data
  const paginatedAssets = sortedFilteredAssets.slice(
    (assetsPage - 1) * itemsPerPage,
    assetsPage * itemsPerPage
  );
  
  const paginatedTrades = filteredTrades.slice(
    (tradesPage - 1) * itemsPerPage,
    tradesPage * itemsPerPage
  );

  // Calculate total pages
  const totalAssetsPages = Math.ceil(sortedFilteredAssets.length / itemsPerPage);
  const totalTradesPages = Math.ceil(filteredTrades.length / itemsPerPage);

  // Reset page when search term changes
  useEffect(() => {
    setAssetsPage(1);
    setTradesPage(1);
  }, [searchTerm]);

  // Reset fetched flags when view changes
  useEffect(() => {
    if (activeView === "Trades") {
      setHasFetchedAssets(false);
    }
  }, [activeView]);

  // Debug assets state changes
  useEffect(() => {
    console.log('Portfolio Spot state:', {
      walletAddress,
      assetsCount: assets.length,
      activeView,
      loading: assetsLoading,
      hasFetched: hasFetchedAssets,
      hasError: assetError
    });
  }, [walletAddress, assets.length, activeView, assetsLoading, hasFetchedAssets, assetError]);

  // Manual refresh function - only refreshes active table
  const refreshData = () => {
    if (activeView === "Assets" && !assetsLoading) {
      setAssets([]);
      setAssetError(null);
      setHasFetchedAssets(false);
      setAssetsPage(1);
      // Clear cache to force fresh fetch
      if (walletAddress) {
        tokenBalanceCache.clearForWallet(walletAddress);
      }
      // Trigger refresh
      setRefreshTrigger(prev => prev + 1);
    } else if (activeView === "Trades") {
      // For trades, we need to trigger a re-fetch through the hook
      // This can be done by changing the filter or wallet address
      setFilterByToken(filterByToken === '' ? null : '');
      setTimeout(() => setFilterByToken(filterByToken), 100);
      setTradesPage(1);
    }
  };

  // Handle navigation to pulse trade
  const handleTradeClick = (mintAddress: string) => {
    navigate(`/pulse-trade/${mintAddress}`);
  };

  // Toggle balance sort order
  const toggleBalanceSort = () => {
    if (balanceSortOrder === null) {
      setBalanceSortOrder('desc');
    } else if (balanceSortOrder === 'desc') {
      setBalanceSortOrder('asc');
    } else {
      setBalanceSortOrder(null);
    }
  };

  return (
    <div className="min-h-full text-white">
      {/* Top Row: Balance and PNL */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">

        {/* Balance Card with Glass Effect */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
          <div className="relative bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300">
            <h2 className="text-lg font-semibold text-white mb-6">Balance</h2>
            
            <div className="space-y-4">
              <div>
                <div className="text-xs text-[#BBBBBB] uppercase tracking-wider mb-1">Total Value</div>
                <div className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  ${totalValue.toFixed(2)}
                </div>
              </div>
              
              <div>
                <div className="text-xs text-[#BBBBBB] uppercase tracking-wider mb-1">Unrealized PNL</div>
                <div className="text-2xl font-semibold flex items-center gap-2">
                  <span>$0.00</span>
                  <span className="text-sm text-gray-500">(0.00%)</span>
                </div>
              </div>
              
              <div className="border-t border-gray-700/50 pt-4">
                <div className="text-xs text-[#BBBBBB] uppercase tracking-wider mb-1">Available Balance</div>
                <div className="text-xl font-semibold text-[#7FFFD4]">$0.00</div>
              </div>
            </div>
          </div>
        </div>

        {/* PNL Chart with Enhanced Styling */}
        <div className="col-span-2 relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
          <div className="relative bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-white">Profit & Loss</h2>
              <div className="flex gap-1 bg-[#1D2226] rounded-lg p-1">
                {["24H", "7D", "30D", "ALL"].map((label) => (
                  <button
                    key={label}
                    onClick={() => setSelectedTimeframe(label)}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-all duration-200 ${
                      selectedTimeframe === label
                        ? "bg-[#7FFFD4]/20 text-[#7FFFD4]"
                        : "text-gray-400 hover:text-white"
                    }`}
                  >
                    {label}
                  </button>
                ))}
              </div>
            </div>
            <div className="h-[200px]">
              <Line
                data={dummyChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: { 
                    legend: { display: false },
                    tooltip: {
                      mode: 'index',
                      intersect: false,
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      titleColor: '#7FFFD4',
                      bodyColor: '#fff',
                      borderColor: '#7FFFD4',
                      borderWidth: 1,
                      padding: 10,
                      displayColors: false,
                      callbacks: {
                        label: (context) => `$${context.parsed.y.toFixed(2)}`
                      }
                    }
                  },
                  scales: { 
                    x: { 
                      display: false,
                      grid: { display: false }
                    }, 
                    y: { 
                      display: false,
                      grid: { display: false }
                    } 
                  },
                  interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Row: Left Side Cards + Asset Table */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mt-6">

        <div className="space-y-4 col-span-1">
          {/* Performance History with Glass Effect */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
            <div className="relative bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-5 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300">
              <h3 className="text-sm font-semibold text-white mb-4 uppercase tracking-wider">Performance History</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Volume</span>
                  <span className="text-sm font-medium text-gray-400">-</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Realized P&L</span>
                  <span className="text-sm font-medium text-gray-400">-</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Realized ROI</span>
                  <span className="text-sm font-medium text-gray-400">-</span>
                </div>
              </div>
            </div>
          </div>

          {/* Filtered Totals with Glass Effect */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
            <div className="relative bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-5 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300">
              <h3 className="text-sm font-semibold text-white mb-4 uppercase tracking-wider">Filtered Totals</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Value</span>
                  <span className="text-sm font-medium text-white">${totalValue.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Cost</span>
                  <span className="text-sm font-medium text-white">$0.00</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Unrealized P&L</span>
                  <span className="text-sm font-medium text-white">$0.00</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-[#BBBBBB] uppercase tracking-wider">Unrealized ROI</span>
                  <span className="text-sm font-medium text-white">0.00%</span>
                </div>
              </div>
            </div>
          </div>

        </div>

        {/* Asset Table with Enhanced Design */}
        <div className="col-span-2 relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-[#7FFFD4]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
          <div className="relative bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300">
            <div className="flex justify-between items-center mb-4">
              <div className="flex gap-1 bg-[#1D2226] rounded-lg p-1">
                <button 
                  onClick={() => setActiveView("Assets")}
                  className={`px-4 py-1.5 rounded-md text-xs font-medium transition-all duration-200 ${
                    activeView === "Assets"
                      ? "bg-[#7FFFD4]/20 text-[#7FFFD4]"
                      : "text-gray-400 hover:text-white"
                  }`}
                >
                  Assets
                </button>
                <button 
                  onClick={() => setActiveView("Trades")}
                  className={`px-4 py-1.5 rounded-md text-xs font-medium transition-all duration-200 ${
                    activeView === "Trades"
                      ? "bg-[#7FFFD4]/20 text-[#7FFFD4]"
                      : "text-gray-400 hover:text-white"
                  }`}
                >
                  Trades
                </button>
              </div>
              <div className="flex gap-2 items-center">
                <div className="relative">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={activeView === "Assets" ? "Search tokens..." : "Search trades..."}
                    className="pl-8 pr-3 py-1.5 bg-[#1D2226] border border-gray-700/50 rounded-lg text-xs text-white placeholder-gray-500 focus:outline-none focus:border-[#7FFFD4]/50 transition-all duration-200 w-48"
                  />
                  <svg className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <button 
                  onClick={refreshData}
                  className="px-3 py-1.5 border border-gray-700/50 hover:border-[#7FFFD4]/50 rounded-lg text-xs font-medium text-gray-400 hover:text-[#7FFFD4] transition-all duration-200 flex items-center gap-1"
                  title="Refresh data"
                  disabled={assetsLoading || userTradeData.isLoading}
                >
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Refresh
                </button>
              </div>
            </div>
            
            {activeView === "Assets" ? (
              <>
                <div className="grid grid-cols-6 text-xs text-gray-500 uppercase tracking-wider border-b border-gray-700/50 pb-3 mb-1">
                  <div className="col-span-2">Token</div>
                  <div 
                    className="text-right cursor-pointer hover:text-[#7FFFD4] transition-colors flex items-center justify-end gap-1"
                    onClick={toggleBalanceSort}
                  >
                    Balance
                    {balanceSortOrder && (
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {balanceSortOrder === 'desc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        )}
                      </svg>
                    )}
                  </div>
                  <div className="text-right">Price</div>
                  <div className="text-right">Value</div>
                  <div className="text-center">Action</div>
                </div>
                {assetsLoading ? (
                  <div className="py-12 text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#7FFFD4]"></div>
                    <p className="text-gray-400 mt-2">Loading assets...</p>
                  </div>
                ) : assetError ? (
                  <div className="py-12 text-center">
                    <div className="text-red-400 mb-4">{assetError}</div>
                    <button 
                      onClick={refreshData}
                      className="px-4 py-2 bg-[#7FFFD4]/20 text-[#7FFFD4] rounded-lg hover:bg-[#7FFFD4]/30 transition-colors"
                    >
                      Retry
                    </button>
                  </div>
                ) : sortedFilteredAssets.length > 0 ? (
                  <div>
                    <div className="space-y-1">
                      {paginatedAssets.map((asset, index) => {
                        // Balance is already in UI format (includes decimals)
                        const uiAmount = asset.balance ? parseFloat(asset.balance) : 0;
                        const displayValue = asset.value || (asset.price ? uiAmount * asset.price : 0);
                        
                        // Check if this is SOL token
                        const isSOL = asset.symbol === 'SOL' || asset.mintAddress === 'So11111111111111111111111111111111111111112';
                        
                        return (
                          <div 
                            key={asset.mintAddress} 
                            className={`grid grid-cols-6 py-3 text-sm border-b border-gray-800/50 hover:bg-gray-800/20 transition-colors items-center ${!isSOL ? 'cursor-pointer' : ''}`}
                            onClick={() => !isSOL && handleTradeClick(asset.mintAddress)}
                          >
                            <div className="col-span-2 flex items-center gap-2">
                              {asset.logo && (
                                <img 
                                  src={asset.logo} 
                                  alt={asset.symbol || 'Token'} 
                                  className="w-8 h-8 rounded-full"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                              )}
                              <div>
                                <div className="font-medium text-white">{asset.symbol || 'Unknown'}</div>
                                <div className="text-xs text-gray-500">{asset.name || asset.mintAddress.slice(0, 8) + '...'}</div>
                              </div>
                            </div>
                            <div className="text-right text-white">
                              {uiAmount.toFixed(4)}
                            </div>
                            <div className="text-right text-gray-400">
                              ${asset.price?.toFixed(4) || '-'}
                            </div>
                            <div className="text-right text-white">
                              ${displayValue.toFixed(2)}
                            </div>
                            <div className="text-center">
                              {!isSOL ? (
                                <button 
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleTradeClick(asset.mintAddress);
                                  }}
                                  className="px-3 py-1 bg-[#7FFFD4]/20 text-[#7FFFD4] rounded text-xs hover:bg-[#7FFFD4]/30 transition-colors"
                                >
                                  Trade
                                </button>
                              ) : (
                                <span className="text-gray-500 text-xs">-</span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    
                    {/* Pagination */}
                    {totalAssetsPages > 1 && (
                      <div className="mt-4 flex items-center justify-between">
                        <div className="text-xs text-gray-400">
                          Showing {((assetsPage - 1) * itemsPerPage) + 1}-{Math.min(assetsPage * itemsPerPage, sortedFilteredAssets.length)} of {sortedFilteredAssets.length} assets
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => setAssetsPage(p => Math.max(1, p - 1))}
                            disabled={assetsPage === 1}
                            className="px-3 py-1 text-xs border border-gray-700/50 rounded hover:border-[#7FFFD4]/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            Previous
                          </button>
                          <div className="flex gap-1">
                            {[...Array(Math.min(5, totalAssetsPages))].map((_, i) => {
                              const pageNum = i + 1;
                              return (
                                <button
                                  key={pageNum}
                                  onClick={() => setAssetsPage(pageNum)}
                                  className={`px-2 py-1 text-xs rounded ${
                                    assetsPage === pageNum 
                                      ? 'bg-[#7FFFD4]/20 text-[#7FFFD4]' 
                                      : 'text-gray-400 hover:text-white'
                                  }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            })}
                          </div>
                          <button
                            onClick={() => setAssetsPage(p => Math.min(totalAssetsPages, p + 1))}
                            disabled={assetsPage === totalAssetsPages}
                            className="px-3 py-1 text-xs border border-gray-700/50 rounded hover:border-[#7FFFD4]/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            Next
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="py-12">
                    <EmptyState
                      title="No Assets Yet"
                      description={walletAddress ? "Your portfolio assets will appear here" : "Connect wallet to view assets"}
                      icon="assets"
                    />
                  </div>
                )}
              </>
            ) : (
              <>
                {/* Token Filter Dropdown for Trades */}
                {assets.length > 0 && (
                  <div className="mb-4 flex justify-end">
                    <select
                      value={filterByToken || ''}
                      onChange={(e) => setFilterByToken(e.target.value || null)}
                      className="px-3 py-1.5 bg-[#1D2226] border border-gray-700/50 rounded-lg text-xs text-white focus:outline-none focus:border-[#7FFFD4]/50 transition-all duration-200"
                    >
                      <option value="">All Tokens</option>
                      {assets.map(asset => (
                        <option key={asset.mintAddress} value={asset.mintAddress}>
                          {asset.symbol || 'Unknown'} {asset.name ? `(${asset.name})` : ''}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                <div className="grid grid-cols-5 text-xs text-gray-500 uppercase tracking-wider border-b border-gray-700/50 pb-3 mb-1">
                  <div>Date</div>
                  <div>Type</div>
                  <div>Amount</div>
                  <div>Price</div>
                  <div>Total</div>
                </div>
                {userTradeData.isLoading ? (
                  <div className="py-12 text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#7FFFD4]"></div>
                    <p className="text-gray-400 mt-2">Loading trades...</p>
                  </div>
                ) : userTradeData.error ? (
                  <div className="py-12 text-center">
                    <div className="text-red-400 mb-4">{userTradeData.error}</div>
                    <button 
                      onClick={refreshData}
                      className="px-4 py-2 bg-[#7FFFD4]/20 text-[#7FFFD4] rounded-lg hover:bg-[#7FFFD4]/30 transition-colors"
                    >
                      Retry
                    </button>
                  </div>
                ) : filteredTrades.length > 0 ? (
                  <div>
                    <div className="space-y-1">
                      {paginatedTrades.map((trade) => (
                        <div key={trade.id} className="grid grid-cols-5 py-3 text-sm border-b border-gray-800/50 hover:bg-gray-800/20 transition-colors">
                          <div className="text-gray-400">
                            {new Date(trade.timestamp).toLocaleDateString()}
                          </div>
                          <div className={trade.type === 'buy' ? 'text-green-500' : 'text-red-500'}>
                            {trade.type.toUpperCase()}
                          </div>
                          <div className="text-white">
                            {trade.amount > 0 ? trade.amount.toFixed(4) : '0.0000'} {trade.tokenSymbol}
                          </div>
                          <div className="text-gray-400">
                            ${trade.price > 0 ? trade.price.toFixed(6) : '0.000000'}
                          </div>
                          <div className="text-white">
                            {trade.total.toFixed(4)} SOL
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {/* Pagination */}
                    {totalTradesPages > 1 && (
                      <div className="mt-4 flex items-center justify-between">
                        <div className="text-xs text-gray-400">
                          Showing {((tradesPage - 1) * itemsPerPage) + 1}-{Math.min(tradesPage * itemsPerPage, filteredTrades.length)} of {filteredTrades.length} trades
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => setTradesPage(p => Math.max(1, p - 1))}
                            disabled={tradesPage === 1}
                            className="px-3 py-1 text-xs border border-gray-700/50 rounded hover:border-[#7FFFD4]/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            Previous
                          </button>
                          <div className="flex gap-1">
                            {[...Array(Math.min(5, totalTradesPages))].map((_, i) => {
                              const pageNum = i + 1;
                              return (
                                <button
                                  key={pageNum}
                                  onClick={() => setTradesPage(pageNum)}
                                  className={`px-2 py-1 text-xs rounded ${
                                    tradesPage === pageNum 
                                      ? 'bg-[#7FFFD4]/20 text-[#7FFFD4]' 
                                      : 'text-gray-400 hover:text-white'
                                  }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            })}
                          </div>
                          <button
                            onClick={() => setTradesPage(p => Math.min(totalTradesPages, p + 1))}
                            disabled={tradesPage === totalTradesPages}
                            className="px-3 py-1 text-xs border border-gray-700/50 rounded hover:border-[#7FFFD4]/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            Next
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="py-12">
                    <EmptyState
                      title="No Trades Yet"
                      description={walletAddress ? "Your trading history will appear here" : "Connect wallet to view trades"}
                      icon="chart"
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioDashboard;
